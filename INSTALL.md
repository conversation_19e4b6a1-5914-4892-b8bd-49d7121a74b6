# 安装指南

## 快速安装

### Chrome浏览器安装步骤

1. **下载插件文件**
   - 确保您已下载完整的插件文件夹
   - 文件夹应包含所有必需的文件（见下方文件清单）

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在扩展程序页面右上角，开启"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择插件文件夹（包含manifest.json的文件夹）
   - 点击"选择文件夹"

5. **验证安装**
   - 插件图标应出现在浏览器工具栏中
   - 点击图标测试弹出窗口是否正常显示

### Edge浏览器安装步骤

1. **打开Edge扩展管理页面**
   - 在Edge浏览器地址栏输入：`edge://extensions/`
   - 或者：菜单 → 扩展

2. **启用开发人员模式**
   - 在扩展页面左下角，开启"开发人员模式"开关

3. **加载插件**
   - 点击"加载解压缩的扩展"按钮
   - 选择插件文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 插件图标应出现在浏览器工具栏中
   - 点击图标测试功能

## 文件清单

安装前请确保以下文件存在：

### 必需文件
- ✅ `manifest.json` - 插件配置文件
- ✅ `popup.html` - 主界面HTML
- ✅ `popup.js` - 主界面逻辑
- ✅ `styles.css` - 主界面样式
- ✅ `report.html` - 报告页面HTML
- ✅ `report.js` - 报告页面逻辑
- ✅ `report-styles.css` - 报告页面样式
- ✅ `history.html` - 历史记录页面HTML
- ✅ `history.js` - 历史记录页面逻辑

### 图标文件
- ✅ `icons/icon16.png` - 16x16像素图标
- ✅ `icons/icon32.png` - 32x32像素图标
- ✅ `icons/icon48.png` - 48x48像素图标
- ✅ `icons/icon128.png` - 128x128像素图标

### 可选文件
- 📄 `README.md` - 说明文档
- 📄 `INSTALL.md` - 安装指南
- 📄 `test.html` - 测试页面

## 常见问题

### Q: 插件无法加载
**A:** 检查以下几点：
- 确保manifest.json文件存在且格式正确
- 确保所有必需文件都在同一文件夹中
- 检查文件路径是否正确

### Q: 插件图标不显示
**A:** 可能的原因：
- 图标文件路径错误
- 图标文件损坏或格式不支持
- 浏览器缓存问题，尝试重新加载插件

### Q: 点击图标没有反应
**A:** 检查：
- popup.html文件是否存在
- JavaScript文件是否有语法错误
- 浏览器控制台是否有错误信息

### Q: API调用失败
**A:** 确认：
- 网络连接正常
- 能够访问Google服务
- API密钥有效（如需更换请修改popup.js）

## 权限说明

插件需要以下权限：

- **storage**: 保存历史记录到本地存储
- **activeTab**: 获取当前标签页信息
- **tabs**: 创建新标签页显示报告
- **host_permissions**: 访问Gemini API服务

## 卸载方法

### Chrome
1. 访问 `chrome://extensions/`
2. 找到"AI项目研究报告生成器"
3. 点击"移除"按钮
4. 确认删除

### Edge
1. 访问 `edge://extensions/`
2. 找到插件
3. 点击"移除"
4. 确认删除

## 更新方法

1. 下载新版本文件
2. 在扩展管理页面点击插件的"重新加载"按钮
3. 或者先移除旧版本，再安装新版本

## 技术支持

如果遇到问题：

1. 检查浏览器控制台错误信息
2. 确认网络连接正常
3. 验证所有文件完整性
4. 尝试重新安装插件

## 开发环境

如需修改插件：

1. 使用文本编辑器打开相关文件
2. 修改后在扩展管理页面点击"重新加载"
3. 测试修改效果
4. 重复上述步骤直到满意

---

**注意**: 本插件仅供学习和研究使用，请遵守相关法律法规。
