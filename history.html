<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录 - AI项目研究报告生成器</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .history-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }
        
        .history-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e1e5e9;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 20px;
            color: #667eea;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        
        .back-btn:hover {
            background: #f0f0f0;
        }
        
        .history-title {
            font-size: 24px;
            color: #333;
            margin: 0;
            flex: 1;
        }
        
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        
        .clear-btn:hover {
            background: #c82333;
        }
        
        .search-box {
            margin-bottom: 25px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #888;
            font-size: 16px;
        }
        
        .history-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .history-item {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .history-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .history-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .history-item-title {
            font-weight: 600;
            color: #333;
            font-size: 16px;
            line-height: 1.4;
            flex: 1;
            margin-right: 15px;
        }
        
        .history-item-date {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
        }
        
        .history-item-preview {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .delete-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #dc3545;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .history-item:hover .delete-btn {
            display: flex;
        }
        
        .delete-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }
        
        .no-history {
            text-align: center;
            color: #888;
            font-style: italic;
            padding: 60px 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px dashed #e1e5e9;
        }
        
        .no-history-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #667eea;
        }
        
        .stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .stats-item {
            text-align: center;
        }
        
        .stats-number {
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
        }
        
        .stats-label {
            font-size: 12px;
            color: #888;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="history-container">
        <div class="history-header">
            <button class="back-btn" id="backBtn" title="返回">←</button>
            <h1 class="history-title">历史记录</h1>
            <button class="clear-btn" id="clearBtn" title="清空所有记录">清空</button>
        </div>
        
        <div class="stats" id="statsSection">
            <div class="stats-item">
                <div class="stats-number" id="totalReports">0</div>
                <div class="stats-label">总报告数</div>
            </div>
            <div class="stats-item">
                <div class="stats-number" id="thisMonth">0</div>
                <div class="stats-label">本月生成</div>
            </div>
            <div class="stats-item">
                <div class="stats-number" id="thisWeek">0</div>
                <div class="stats-label">本周生成</div>
            </div>
        </div>
        
        <div class="search-box">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索历史记录...">
            <span class="search-icon">🔍</span>
        </div>
        
        <div id="historyList" class="history-list">
            <div class="loading">正在加载历史记录...</div>
        </div>
    </div>
    
    <script src="history.js"></script>
</body>
</html>
