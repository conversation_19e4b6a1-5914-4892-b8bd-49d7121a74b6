// data-integrator.js - 多源数据融合逻辑

class DataIntegrator {
    constructor() {
        this.weights = {
            ai: 0.4,        // AI生成数据权重
            crawler: 0.6    // 爬虫数据权重
        };
    }

    /**
     * 融合所有数据：爬虫数据 + API数据
     * @param {Object} crawlerData - 爬虫抓取的数据
     * @param {Object} apiData - API生成的数据
     * @param {string} userInput - 用户原始输入
     * @returns {string} - 融合后的数据文本
     */
    fuseAllData(crawlerData, apiData, userInput) {
        let fusedData = `项目主题: ${userInput}\n\n`;

        // 添加爬虫数据
        if (crawlerData && crawlerData.content) {
            fusedData += `=== 网页数据 ===\n`;

            const metadata = crawlerData.metadata || {};
            if (metadata.title) {
                fusedData += `页面标题: ${metadata.title}\n`;
            }

            if (metadata.description) {
                fusedData += `页面描述: ${metadata.description}\n`;
            }

            if (crawlerData.sourceUrl) {
                fusedData += `数据来源: ${crawlerData.sourceUrl}\n`;
            }

            fusedData += `页面内容: ${this.cleanAndSummarizeContent(crawlerData.content)}\n\n`;
        }

        // 添加API数据
        if (apiData) {
            fusedData += `=== AI分析数据 ===\n`;

            if (apiData.overview) {
                fusedData += `项目概述: ${apiData.overview}\n\n`;
            }

            if (apiData.goals) {
                fusedData += `研究目标: ${apiData.goals}\n\n`;
            }

            if (apiData.technical) {
                fusedData += `技术分析: ${apiData.technical}\n\n`;
            }

            if (apiData.market) {
                fusedData += `市场前景: ${apiData.market}\n\n`;
            }

            if (apiData.risks) {
                fusedData += `风险评估: ${apiData.risks}\n\n`;
            }

            if (apiData.recommendations) {
                fusedData += `建议结论: ${apiData.recommendations}\n\n`;
            }
        }

        // 如果没有任何额外数据，返回基础信息
        if (!crawlerData && !apiData) {
            fusedData += `注意: 未能收集到额外的网页数据或API数据，报告将基于项目主题进行分析。\n\n`;
        }

        return fusedData;
    }

    /**
     * 清理和总结内容
     * @param {string} content - 原始内容
     * @returns {string} - 清理后的内容
     */
    cleanAndSummarizeContent(content) {
        if (!content) return '';

        // 分割成句子并过滤
        const sentences = content.split(/[.。!！?？]/)
            .map(s => s.trim())
            .filter(s => s.length > 20 && s.length < 200)
            .slice(0, 10); // 只取前10个有效句子

        return sentences.join('。') + '。';
    }

    /**
     * 将融合数据格式化为报告（备用方案）
     * @param {string} fusedData - 融合后的数据
     * @param {string} originalTopic - 原始主题
     * @returns {Object} - 格式化的报告
     */
    formatFusedDataAsReport(fusedData, originalTopic) {
        return {
            overview: `基于收集到的数据，${originalTopic}项目的相关信息如下：\n\n${fusedData.substring(0, 500)}...`,
            goals: '基于融合数据分析，项目目标正在整理中...',
            technical: '基于融合数据分析，技术信息正在整理中...',
            market: '基于融合数据分析，市场信息正在整理中...',
            risks: '基于融合数据分析，风险评估正在整理中...',
            recommendations: '基于融合数据分析，建议正在整理中...',
            dataSource: '融合数据（爬虫 + API）',
            sourceInfo: '本报告基于网页爬虫数据和API数据的融合分析生成'
        };
    }

    /**
     * 格式化仅包含AI数据的报告
     * @param {Object} aiData - AI数据
     * @param {string} userInput - 用户输入
     * @returns {Object} - 格式化的报告
     */
    formatAIOnlyReport(aiData, userInput) {
        return {
            overview: aiData.overview || '暂无项目概述信息',
            goals: aiData.goals || '暂无研究目标信息',
            technical: aiData.technical || '暂无技术分析信息',
            market: aiData.market || '暂无市场前景信息',
            risks: aiData.risks || '暂无风险评估信息',
            recommendations: aiData.recommendations || '暂无建议与结论',
            dataSource: 'AI生成',
            sourceInfo: '本报告完全基于AI分析生成'
        };
    }

    /**
     * 格式化仅包含爬虫数据的报告
     * @param {Object} crawlerData - 爬虫数据
     * @param {string} userInput - 用户输入
     * @returns {Object} - 格式化的报告
     */
    formatCrawlerOnlyReport(crawlerData, userInput) {
        const content = crawlerData.content || '';
        const metadata = crawlerData.metadata || {};
        
        return {
            overview: this.extractOverviewFromContent(content, metadata),
            goals: this.extractGoalsFromContent(content),
            technical: this.extractTechnicalFromContent(content),
            market: this.extractMarketFromContent(content),
            risks: this.extractRisksFromContent(content),
            recommendations: this.extractRecommendationsFromContent(content),
            dataSource: '网页抓取',
            sourceInfo: `数据来源: ${crawlerData.sourceUrl || '未知'}\n页面标题: ${metadata.title || '未知'}`
        };
    }

    /**
     * 格式化整合数据的报告
     * @param {Object} aiData - AI数据
     * @param {Object} crawlerData - 爬虫数据
     * @param {string} userInput - 用户输入
     * @returns {Object} - 整合后的报告
     */
    formatIntegratedReport(aiData, crawlerData, userInput) {
        const content = crawlerData.content || '';
        const metadata = crawlerData.metadata || {};
        
        return {
            overview: this.mergeContent(
                aiData.overview,
                this.extractOverviewFromContent(content, metadata),
                '项目概述'
            ),
            goals: this.mergeContent(
                aiData.goals,
                this.extractGoalsFromContent(content),
                '研究目标'
            ),
            technical: this.mergeContent(
                aiData.technical,
                this.extractTechnicalFromContent(content),
                '技术分析'
            ),
            market: this.mergeContent(
                aiData.market,
                this.extractMarketFromContent(content),
                '市场前景'
            ),
            risks: this.mergeContent(
                aiData.risks,
                this.extractRisksFromContent(content),
                '风险评估'
            ),
            recommendations: this.mergeContent(
                aiData.recommendations,
                this.extractRecommendationsFromContent(content),
                '建议与结论'
            ),
            dataSource: 'AI分析 + 网页数据',
            sourceInfo: `AI分析结合实时网页数据\n数据来源: ${crawlerData.sourceUrl || '未知'}\n页面标题: ${metadata.title || '未知'}`
        };
    }

    /**
     * 合并AI内容和爬虫内容
     * @param {string} aiContent - AI生成的内容
     * @param {string} crawlerContent - 爬虫提取的内容
     * @param {string} section - 章节名称
     * @returns {string} - 合并后的内容
     */
    mergeContent(aiContent, crawlerContent, section) {
        const ai = aiContent || '';
        const crawler = crawlerContent || '';
        
        if (!ai && !crawler) {
            return `暂无${section}相关信息`;
        }
        
        if (!ai) return crawler;
        if (!crawler) return ai;
        
        // 如果两者都有内容，进行智能合并
        return `**AI分析:**\n${ai}\n\n**实时数据:**\n${crawler}`;
    }

    /**
     * 从内容中提取项目概述
     * @param {string} content - 页面内容
     * @param {Object} metadata - 页面元数据
     * @returns {string} - 提取的概述
     */
    extractOverviewFromContent(content, metadata) {
        if (!content) return '';
        
        // 优先使用页面描述
        if (metadata.description) {
            return metadata.description;
        }
        
        // 提取前几句话作为概述
        const sentences = content.split(/[.。!！?？]/).filter(s => s.trim().length > 20);
        return sentences.slice(0, 3).join('。') + '。';
    }

    /**
     * 从内容中提取研究目标
     * @param {string} content - 页面内容
     * @returns {string} - 提取的目标
     */
    extractGoalsFromContent(content) {
        if (!content) return '';
        
        const goalKeywords = ['目标', '目的', '宗旨', '使命', '愿景', 'goal', 'objective', 'mission'];
        const sentences = content.split(/[.。!！?？]/);
        
        const goalSentences = sentences.filter(sentence => 
            goalKeywords.some(keyword => sentence.toLowerCase().includes(keyword))
        );
        
        return goalSentences.slice(0, 3).join('。') + '。' || '从页面内容中未能提取到明确的研究目标信息。';
    }

    /**
     * 从内容中提取技术分析
     * @param {string} content - 页面内容
     * @returns {string} - 提取的技术信息
     */
    extractTechnicalFromContent(content) {
        if (!content) return '';
        
        const techKeywords = ['技术', '算法', '架构', '系统', '平台', '框架', 'technology', 'technical', 'system'];
        const sentences = content.split(/[.。!！?？]/);
        
        const techSentences = sentences.filter(sentence => 
            techKeywords.some(keyword => sentence.toLowerCase().includes(keyword))
        );
        
        return techSentences.slice(0, 3).join('。') + '。' || '从页面内容中未能提取到详细的技术信息。';
    }

    /**
     * 从内容中提取市场信息
     * @param {string} content - 页面内容
     * @returns {string} - 提取的市场信息
     */
    extractMarketFromContent(content) {
        if (!content) return '';
        
        const marketKeywords = ['市场', '商业', '盈利', '用户', '客户', '竞争', 'market', 'business', 'commercial'];
        const sentences = content.split(/[.。!！?？]/);
        
        const marketSentences = sentences.filter(sentence => 
            marketKeywords.some(keyword => sentence.toLowerCase().includes(keyword))
        );
        
        return marketSentences.slice(0, 3).join('。') + '。' || '从页面内容中未能提取到市场相关信息。';
    }

    /**
     * 从内容中提取风险信息
     * @param {string} content - 页面内容
     * @returns {string} - 提取的风险信息
     */
    extractRisksFromContent(content) {
        if (!content) return '';
        
        const riskKeywords = ['风险', '挑战', '问题', '困难', '限制', 'risk', 'challenge', 'limitation'];
        const sentences = content.split(/[.。!！?？]/);
        
        const riskSentences = sentences.filter(sentence => 
            riskKeywords.some(keyword => sentence.toLowerCase().includes(keyword))
        );
        
        return riskSentences.slice(0, 3).join('。') + '。' || '从页面内容中未能识别到明确的风险因素。';
    }

    /**
     * 从内容中提取建议信息
     * @param {string} content - 页面内容
     * @returns {string} - 提取的建议信息
     */
    extractRecommendationsFromContent(content) {
        if (!content) return '';
        
        const recKeywords = ['建议', '推荐', '总结', '结论', '未来', 'recommend', 'suggest', 'conclusion'];
        const sentences = content.split(/[.。!！?？]/);
        
        const recSentences = sentences.filter(sentence => 
            recKeywords.some(keyword => sentence.toLowerCase().includes(keyword))
        );
        
        return recSentences.slice(0, 3).join('。') + '。' || '基于页面内容，建议进一步深入研究相关领域。';
    }

    /**
     * 格式化错误报告
     * @param {string} userInput - 用户输入
     * @param {string} errorMessage - 错误信息
     * @returns {Object} - 错误报告
     */
    formatErrorReport(userInput, errorMessage) {
        return {
            overview: `项目主题: ${userInput}\n\n由于数据获取过程中出现问题，无法生成完整的项目概述。`,
            goals: '数据获取失败，无法分析研究目标。',
            technical: '数据获取失败，无法进行技术分析。',
            market: '数据获取失败，无法评估市场前景。',
            risks: '数据获取失败，无法进行风险评估。',
            recommendations: '建议检查网络连接或稍后重试。',
            dataSource: '错误报告',
            sourceInfo: `错误信息: ${errorMessage}`
        };
    }
}

// 创建全局实例
const dataIntegrator = new DataIntegrator();

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.dataIntegrator = dataIntegrator;
}
