// crawler.js - 主爬虫逻辑控制器

class WebCrawler {
    constructor() {
        this.maxConcurrentTabs = 3;
        this.pageTimeout = 15000; // 15秒超时
        this.activeTabs = new Set();
        this.crawledUrls = new Set();
        this.extractedData = [];
    }

    /**
     * 开始爬虫任务
     * @param {string} input - 用户输入
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} - 爬虫结果
     */
    async startCrawling(input, progressCallback = () => {}) {
        try {
            progressCallback({ stage: 'parsing', message: '解析输入类型...' });
            
            // 解析输入类型
            const inputInfo = inputParser.identifyInputType(input);
            
            let targetUrl = '';
            
            switch (inputInfo.type) {
                case 'url':
                    targetUrl = inputInfo.normalizedUrl;
                    break;
                case 'domain':
                    targetUrl = inputInfo.normalizedUrl;
                    break;
                case 'project':
                    // 对于项目名称，暂时跳过搜索引擎查找，直接返回空结果
                    progressCallback({ stage: 'complete', message: '项目名称搜索功能开发中...' });
                    return { content: '', links: [], metadata: {} };
                default:
                    throw new Error('无法识别的输入类型');
            }

            progressCallback({ stage: 'crawling', message: `开始抓取: ${targetUrl}` });
            
            // 执行单页面抓取
            const result = await this.crawlSinglePage(targetUrl);
            
            progressCallback({ stage: 'complete', message: '抓取完成' });
            
            return result;
        } catch (error) {
            console.error('爬虫任务失败:', error);
            progressCallback({ stage: 'error', message: `抓取失败: ${error.message}` });
            throw error;
        }
    }

    /**
     * 爬取单个页面
     * @param {string} url - 目标URL
     * @returns {Promise<Object>} - 页面数据
     */
    async crawlSinglePage(url) {
        let tabId = null;
        
        try {
            // 创建新标签页
            const tab = await chrome.tabs.create({
                url: url,
                active: false
            });
            
            tabId = tab.id;
            this.activeTabs.add(tabId);
            
            // 等待页面加载
            await this.waitForPageLoad(tabId);
            
            // 注入内容脚本
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-script.js']
            });
            
            // 提取页面内容
            const result = await this.extractPageContent(tabId);
            
            // 记录已爬取的URL
            this.crawledUrls.add(url);
            
            return {
                content: result.content || '',
                links: result.links || [],
                metadata: result.metadata || {},
                sourceUrl: url
            };
            
        } catch (error) {
            console.error(`页面抓取失败 ${url}:`, error);
            return {
                content: '',
                links: [],
                metadata: {},
                sourceUrl: url,
                error: error.message
            };
        } finally {
            // 清理标签页
            if (tabId) {
                await this.closeTab(tabId);
            }
        }
    }

    /**
     * 等待页面加载完成
     * @param {number} tabId - 标签页ID
     * @returns {Promise<void>}
     */
    async waitForPageLoad(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('页面加载超时'));
            }, this.pageTimeout);

            const checkStatus = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        clearTimeout(timeout);
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    if (tab.status === 'complete') {
                        clearTimeout(timeout);
                        // 额外等待一点时间确保JavaScript执行完成
                        setTimeout(resolve, 2000);
                    } else {
                        setTimeout(checkStatus, 500);
                    }
                });
            };

            checkStatus();
        });
    }

    /**
     * 提取页面内容
     * @param {number} tabId - 标签页ID
     * @returns {Promise<Object>} - 提取的内容
     */
    async extractPageContent(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('内容提取超时'));
            }, 10000);

            chrome.tabs.sendMessage(tabId, { action: 'extractContent' }, (response) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                    return;
                }
                
                if (response) {
                    resolve(response);
                } else {
                    reject(new Error('未收到内容提取响应'));
                }
            });
        });
    }

    /**
     * 关闭标签页
     * @param {number} tabId - 标签页ID
     * @returns {Promise<void>}
     */
    async closeTab(tabId) {
        try {
            if (this.activeTabs.has(tabId)) {
                await chrome.tabs.remove(tabId);
                this.activeTabs.delete(tabId);
            }
        } catch (error) {
            console.error(`关闭标签页失败 ${tabId}:`, error);
        }
    }

    /**
     * 清理所有活动标签页
     * @returns {Promise<void>}
     */
    async cleanup() {
        const tabIds = Array.from(this.activeTabs);
        for (const tabId of tabIds) {
            await this.closeTab(tabId);
        }
        this.activeTabs.clear();
        this.crawledUrls.clear();
        this.extractedData = [];
    }

    /**
     * 验证URL是否可访问
     * @param {string} url - 要验证的URL
     * @returns {Promise<boolean>} - 是否可访问
     */
    async validateUrl(url) {
        try {
            // 尝试创建一个隐藏标签页来测试URL
            const tab = await chrome.tabs.create({
                url: url,
                active: false
            });
            
            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查标签页状态
            const updatedTab = await chrome.tabs.get(tab.id);
            const isValid = updatedTab.status === 'complete' && !updatedTab.url.includes('error');
            
            // 关闭测试标签页
            await chrome.tabs.remove(tab.id);
            
            return isValid;
        } catch (error) {
            console.error('URL验证失败:', error);
            return false;
        }
    }

    /**
     * 获取爬虫统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        return {
            crawledUrls: this.crawledUrls.size,
            activeTabs: this.activeTabs.size,
            extractedPages: this.extractedData.length
        };
    }
}

// 创建全局实例
const webCrawler = new WebCrawler();

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.webCrawler = webCrawler;
}
