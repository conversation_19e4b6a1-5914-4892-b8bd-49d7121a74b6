// crawler.js - 主爬虫逻辑控制器

class WebCrawler {
    constructor() {
        this.maxConcurrentTabs = 3;
        this.pageTimeout = 15000; // 15秒超时
        this.activeTabs = new Set();
        this.crawledUrls = new Set();
        this.extractedData = [];
        this.maxLinksPerLevel = 10; // 每层级最大链接数
        this.maxTotalPages = 25; // 总页面数限制
        this.baseDomain = ''; // 基础域名
        this.currentLevel = 1; // 当前抓取层级
    }

    /**
     * 开始爬虫任务
     * @param {string} input - 用户输入
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} - 爬虫结果
     */
    async startCrawling(input, progressCallback = () => {}) {
        try {
            progressCallback({ stage: 'parsing', message: '解析输入类型...' });

            // 解析输入类型
            const inputInfo = inputParser.identifyInputType(input);

            let targetUrl = '';

            switch (inputInfo.type) {
                case 'url':
                    targetUrl = inputInfo.normalizedUrl;
                    break;
                case 'domain':
                    targetUrl = inputInfo.normalizedUrl;
                    break;
                case 'project':
                    // 对于项目名称，尝试搜索
                    progressCallback({ stage: 'searching', message: '搜索项目信息...' });
                    targetUrl = await this.searchProjectUrl(input);
                    if (!targetUrl) {
                        return { content: '', links: [], metadata: {}, error: '未找到相关项目网站' };
                    }
                    break;
                default:
                    throw new Error('无法识别的输入类型');
            }

            // 设置基础域名
            this.baseDomain = inputParser.extractDomain(targetUrl);

            progressCallback({ stage: 'crawling', message: `开始3层级抓取: ${targetUrl}` });

            // 执行3层级递进抓取
            const result = await this.crawlMultiLevel(targetUrl, progressCallback);

            progressCallback({ stage: 'complete', message: '3层级抓取完成' });

            return result;
        } catch (error) {
            console.error('爬虫任务失败:', error);
            progressCallback({ stage: 'error', message: `抓取失败: ${error.message}` });
            throw error;
        }
    }

    /**
     * 执行3层级递进抓取
     * @param {string} startUrl - 起始URL
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Object>} - 抓取结果
     */
    async crawlMultiLevel(startUrl, progressCallback) {
        const allData = {
            content: '',
            links: [],
            metadata: {},
            sourceUrls: [],
            levelData: {
                level1: [],
                level2: [],
                level3: []
            }
        };

        try {
            // 第1层级：抓取起始页面
            progressCallback({ stage: 'level1', message: '第1层级：抓取主页面...' });
            this.currentLevel = 1;
            const level1Data = await this.crawlSinglePage(startUrl);
            allData.levelData.level1.push(level1Data);

            // 收集第1层级的有效链接
            const level1Links = this.filterAndLimitLinks(level1Data.links || [], 1);

            if (level1Links.length > 0) {
                // 第2层级：抓取第1层级发现的链接
                progressCallback({ stage: 'level2', message: `第2层级：抓取${level1Links.length}个关联页面...` });
                this.currentLevel = 2;
                const level2Results = await this.crawlBatch(level1Links, progressCallback);
                allData.levelData.level2 = level2Results;

                // 收集第2层级的新链接
                const level2Links = [];
                level2Results.forEach(result => {
                    if (result.links) {
                        level2Links.push(...result.links);
                    }
                });

                const filteredLevel2Links = this.filterAndLimitLinks(level2Links, 2);

                if (filteredLevel2Links.length > 0) {
                    // 第3层级：抓取第2层级发现的链接
                    progressCallback({ stage: 'level3', message: `第3层级：抓取${filteredLevel2Links.length}个深度页面...` });
                    this.currentLevel = 3;
                    const level3Results = await this.crawlBatch(filteredLevel2Links, progressCallback);
                    allData.levelData.level3 = level3Results;
                }
            }

            // 整合所有数据
            return this.consolidateData(allData);

        } catch (error) {
            console.error('多层级抓取失败:', error);
            return this.consolidateData(allData); // 返回已收集的数据
        }
    }

    /**
     * 批量抓取页面
     * @param {Array<string>} urls - URL列表
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Array>} - 抓取结果数组
     */
    async crawlBatch(urls, progressCallback) {
        const results = [];
        const batches = this.createBatches(urls, this.maxConcurrentTabs);

        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            progressCallback({
                stage: `level${this.currentLevel}`,
                message: `第${this.currentLevel}层级：处理批次${i + 1}/${batches.length}...`
            });

            const batchPromises = batch.map(url => this.crawlSinglePage(url));
            const batchResults = await Promise.allSettled(batchPromises);

            batchResults.forEach(result => {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                }
            });

            // 批次间短暂延迟，避免过度负载
            if (i < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return results;
    }

    /**
     * 创建批次
     * @param {Array} items - 项目数组
     * @param {number} batchSize - 批次大小
     * @returns {Array<Array>} - 批次数组
     */
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * 爬取单个页面
     * @param {string} url - 目标URL
     * @returns {Promise<Object>} - 页面数据
     */
    async crawlSinglePage(url) {
        let tabId = null;
        
        try {
            // 创建新标签页
            const tab = await chrome.tabs.create({
                url: url,
                active: false
            });
            
            tabId = tab.id;
            this.activeTabs.add(tabId);
            
            // 等待页面加载
            await this.waitForPageLoad(tabId);
            
            // 注入内容脚本
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-script.js']
            });
            
            // 提取页面内容
            const result = await this.extractPageContent(tabId);
            
            // 记录已爬取的URL
            this.crawledUrls.add(url);
            
            return {
                content: result.content || '',
                links: result.links || [],
                metadata: result.metadata || {},
                sourceUrl: url
            };
            
        } catch (error) {
            console.error(`页面抓取失败 ${url}:`, error);
            return {
                content: '',
                links: [],
                metadata: {},
                sourceUrl: url,
                error: error.message
            };
        } finally {
            // 清理标签页
            if (tabId) {
                await this.closeTab(tabId);
            }
        }
    }

    /**
     * 等待页面加载完成
     * @param {number} tabId - 标签页ID
     * @returns {Promise<void>}
     */
    async waitForPageLoad(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('页面加载超时'));
            }, this.pageTimeout);

            const checkStatus = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        clearTimeout(timeout);
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    if (tab.status === 'complete') {
                        clearTimeout(timeout);
                        // 额外等待一点时间确保JavaScript执行完成
                        setTimeout(resolve, 2000);
                    } else {
                        setTimeout(checkStatus, 500);
                    }
                });
            };

            checkStatus();
        });
    }

    /**
     * 提取页面内容
     * @param {number} tabId - 标签页ID
     * @returns {Promise<Object>} - 提取的内容
     */
    async extractPageContent(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('内容提取超时'));
            }, 10000);

            chrome.tabs.sendMessage(tabId, { action: 'extractContent' }, (response) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                    return;
                }
                
                if (response) {
                    resolve(response);
                } else {
                    reject(new Error('未收到内容提取响应'));
                }
            });
        });
    }

    /**
     * 关闭标签页
     * @param {number} tabId - 标签页ID
     * @returns {Promise<void>}
     */
    async closeTab(tabId) {
        try {
            if (this.activeTabs.has(tabId)) {
                await chrome.tabs.remove(tabId);
                this.activeTabs.delete(tabId);
            }
        } catch (error) {
            console.error(`关闭标签页失败 ${tabId}:`, error);
        }
    }

    /**
     * 清理所有活动标签页
     * @returns {Promise<void>}
     */
    async cleanup() {
        const tabIds = Array.from(this.activeTabs);
        for (const tabId of tabIds) {
            await this.closeTab(tabId);
        }
        this.activeTabs.clear();
        this.crawledUrls.clear();
        this.extractedData = [];
    }

    /**
     * 智能过滤和限制链接
     * @param {Array<string>} links - 链接数组
     * @param {number} level - 当前层级
     * @returns {Array<string>} - 过滤后的链接
     */
    filterAndLimitLinks(links, level) {
        if (!links || links.length === 0) return [];

        // 去重
        const uniqueLinks = [...new Set(links)];

        // 过滤已爬取的URL
        const unCrawledLinks = uniqueLinks.filter(link => !this.crawledUrls.has(link));

        // 智能过滤
        const filteredLinks = unCrawledLinks.filter(link => this.isValidLinkForLevel(link, level));

        // 按优先级排序
        const sortedLinks = this.sortLinksByPriority(filteredLinks, level);

        // 限制数量
        const maxLinks = Math.max(1, this.maxLinksPerLevel - level); // 层级越深，链接越少
        return sortedLinks.slice(0, maxLinks);
    }

    /**
     * 检查链接是否适合当前层级
     * @param {string} url - 链接URL
     * @param {number} level - 层级
     * @returns {boolean} - 是否有效
     */
    isValidLinkForLevel(url, level) {
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname;

            // 基本有效性检查
            if (!inputParser.isValidLink(url, this.baseDomain)) {
                return false;
            }

            // 层级特定过滤
            switch (level) {
                case 1:
                    // 第1层级：只要同域名或相关域名
                    return inputParser.isRelatedDomain(url, this.baseDomain);

                case 2:
                    // 第2层级：更严格，优先主要页面
                    return this.isPrimaryPage(url) && inputParser.isRelatedDomain(url, this.baseDomain);

                case 3:
                    // 第3层级：最严格，只要核心内容页面
                    return this.isCoreContentPage(url) && inputParser.isRelatedDomain(url, this.baseDomain);

                default:
                    return false;
            }
        } catch (error) {
            return false;
        }
    }

    /**
     * 检查是否为主要页面
     * @param {string} url - URL
     * @returns {boolean} - 是否为主要页面
     */
    isPrimaryPage(url) {
        const primaryKeywords = [
            'about', 'product', 'service', 'solution', 'feature',
            'technology', 'team', 'company', 'overview', 'intro',
            '关于', '产品', '服务', '解决方案', '功能', '技术', '团队', '公司'
        ];

        const urlLower = url.toLowerCase();
        return primaryKeywords.some(keyword => urlLower.includes(keyword));
    }

    /**
     * 检查是否为核心内容页面
     * @param {string} url - URL
     * @returns {boolean} - 是否为核心内容页面
     */
    isCoreContentPage(url) {
        const coreKeywords = [
            'detail', 'info', 'document', 'spec', 'guide', 'manual',
            'research', 'paper', 'report', 'analysis', 'case',
            '详情', '信息', '文档', '规格', '指南', '手册', '研究', '报告', '分析', '案例'
        ];

        const urlLower = url.toLowerCase();
        return coreKeywords.some(keyword => urlLower.includes(keyword));
    }

    /**
     * 按优先级排序链接
     * @param {Array<string>} links - 链接数组
     * @param {number} level - 层级
     * @returns {Array<string>} - 排序后的链接
     */
    sortLinksByPriority(links, level) {
        return links.sort((a, b) => {
            const scoreA = this.calculateLinkScore(a, level);
            const scoreB = this.calculateLinkScore(b, level);
            return scoreB - scoreA; // 降序排列
        });
    }

    /**
     * 计算链接优先级分数
     * @param {string} url - URL
     * @param {number} level - 层级
     * @returns {number} - 分数
     */
    calculateLinkScore(url, level) {
        let score = 0;
        const urlLower = url.toLowerCase();

        // 基础分数：同域名
        if (inputParser.isRelatedDomain(url, this.baseDomain)) {
            score += 10;
        }

        // 路径深度惩罚（路径越深分数越低）
        const pathDepth = url.split('/').length - 3;
        score -= pathDepth * 2;

        // 关键词加分
        const highValueKeywords = [
            'about', 'product', 'technology', 'research', 'document',
            '关于', '产品', '技术', '研究', '文档'
        ];

        highValueKeywords.forEach(keyword => {
            if (urlLower.includes(keyword)) {
                score += 5;
            }
        });

        // 避免低价值页面
        const lowValueKeywords = [
            'login', 'register', 'cart', 'checkout', 'privacy', 'terms',
            'contact', 'support', 'help', 'faq', 'sitemap',
            '登录', '注册', '购物车', '隐私', '条款', '联系', '支持', '帮助'
        ];

        lowValueKeywords.forEach(keyword => {
            if (urlLower.includes(keyword)) {
                score -= 10;
            }
        });

        return score;
    }

    /**
     * 整合所有层级的数据
     * @param {Object} allData - 所有数据
     * @returns {Object} - 整合后的数据
     */
    consolidateData(allData) {
        let consolidatedContent = '';
        let allLinks = [];
        let consolidatedMetadata = {};
        let sourceUrls = [];

        // 整合各层级数据
        ['level1', 'level2', 'level3'].forEach((level, index) => {
            const levelData = allData.levelData[level];
            if (levelData && levelData.length > 0) {
                levelData.forEach(pageData => {
                    if (pageData.content) {
                        consolidatedContent += `\n=== 第${index + 1}层级数据 ===\n${pageData.content}\n`;
                    }
                    if (pageData.links) {
                        allLinks.push(...pageData.links);
                    }
                    if (pageData.sourceUrl) {
                        sourceUrls.push(pageData.sourceUrl);
                    }
                    if (pageData.metadata && index === 0) {
                        // 使用第1层级的元数据作为主要元数据
                        consolidatedMetadata = pageData.metadata;
                    }
                });
            }
        });

        // 清理和去重
        const cleanedContent = this.cleanConsolidatedContent(consolidatedContent);
        const uniqueLinks = [...new Set(allLinks)];

        return {
            content: cleanedContent,
            links: uniqueLinks.slice(0, 50), // 限制链接数量
            metadata: consolidatedMetadata,
            sourceUrls: sourceUrls,
            levelStats: {
                level1Pages: allData.levelData.level1.length,
                level2Pages: allData.levelData.level2.length,
                level3Pages: allData.levelData.level3.length,
                totalPages: allData.levelData.level1.length +
                           allData.levelData.level2.length +
                           allData.levelData.level3.length
            }
        };
    }

    /**
     * 清理整合后的内容
     * @param {string} content - 原始内容
     * @returns {string} - 清理后的内容
     */
    cleanConsolidatedContent(content) {
        if (!content) return '';

        // 移除重复的段落
        const paragraphs = content.split('\n').filter(p => p.trim().length > 0);
        const uniqueParagraphs = [];
        const seen = new Set();

        paragraphs.forEach(paragraph => {
            const normalized = paragraph.trim().toLowerCase();
            if (!seen.has(normalized) && normalized.length > 20) {
                seen.add(normalized);
                uniqueParagraphs.push(paragraph.trim());
            }
        });

        // 限制总长度
        const maxLength = 5000;
        let result = uniqueParagraphs.join('\n');

        if (result.length > maxLength) {
            result = result.substring(0, maxLength) + '...\n[内容已截断]';
        }

        return result;
    }

    /**
     * 搜索项目URL（简单实现）
     * @param {string} projectName - 项目名称
     * @returns {Promise<string>} - 找到的URL
     */
    async searchProjectUrl(projectName) {
        // 简单的搜索逻辑，可以后续扩展
        const searchQueries = [
            `${projectName} 官网`,
            `${projectName} official website`,
            `${projectName} github`,
            `${projectName} project`
        ];

        // 这里可以集成搜索引擎API，暂时返回null
        console.log(`搜索项目: ${projectName}`);
        return null;
    }

    /**
     * 验证URL是否可访问
     * @param {string} url - 要验证的URL
     * @returns {Promise<boolean>} - 是否可访问
     */
    async validateUrl(url) {
        try {
            // 尝试创建一个隐藏标签页来测试URL
            const tab = await chrome.tabs.create({
                url: url,
                active: false
            });
            
            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查标签页状态
            const updatedTab = await chrome.tabs.get(tab.id);
            const isValid = updatedTab.status === 'complete' && !updatedTab.url.includes('error');
            
            // 关闭测试标签页
            await chrome.tabs.remove(tab.id);
            
            return isValid;
        } catch (error) {
            console.error('URL验证失败:', error);
            return false;
        }
    }

    /**
     * 获取爬虫统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        return {
            crawledUrls: this.crawledUrls.size,
            activeTabs: this.activeTabs.size,
            extractedPages: this.extractedData.length
        };
    }
}

// 创建全局实例
const webCrawler = new WebCrawler();

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.webCrawler = webCrawler;
}
