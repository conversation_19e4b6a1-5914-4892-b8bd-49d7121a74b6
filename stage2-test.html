<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第二阶段功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f7fa;
        }
        
        .test-section {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.new { background: #e8f4fd; color: #1976d2; }
        .status.enhanced { background: #f0fff4; color: #388e3c; }
        .status.improved { background: #fff3e0; color: #f57c00; }
        
        .test-case {
            background: #e8f4fd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #bbdefb;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h1>🚀 第二阶段扩展功能测试</h1>
        <p>本页面用于测试和验证第二阶段实现的扩展功能：3层级递进抓取、智能链接过滤、高级数据清洗。</p>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">层级递进抓取</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">25+</div>
                <div class="stat-label">智能过滤规则</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10+</div>
                <div class="stat-label">数据清洗算法</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5</div>
                <div class="stat-label">质量评估维度</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 新增功能概览</h2>
        
        <div class="feature-card">
            <h3>1. 3层级递进抓取 <span class="status new">NEW</span></h3>
            <p><strong>功能描述：</strong>从起始页面开始，递进式抓取3个层级的关联页面</p>
            <ul>
                <li><strong>第1层级：</strong>抓取目标页面，提取所有有效链接</li>
                <li><strong>第2层级：</strong>抓取第1层级发现的关联页面</li>
                <li><strong>第3层级：</strong>抓取第2层级发现的深度页面</li>
            </ul>
            <p><strong>技术特点：</strong>并发控制、超时管理、智能去重</p>
        </div>

        <div class="feature-card">
            <h3>2. 智能链接过滤 <span class="status new">NEW</span></h3>
            <p><strong>功能描述：</strong>多维度智能过滤，确保抓取高质量相关页面</p>
            <ul>
                <li><strong>域名关联性：</strong>同域名和相关子域名优先</li>
                <li><strong>内容相关性：</strong>基于URL路径和关键词评分</li>
                <li><strong>层级适应性：</strong>不同层级采用不同过滤策略</li>
                <li><strong>质量控制：</strong>排除低价值页面（登录、购物车等）</li>
            </ul>
        </div>

        <div class="feature-card">
            <h3>3. 高级数据清洗 <span class="status new">NEW</span></h3>
            <p><strong>功能描述：</strong>多算法数据清洗，提升数据质量</p>
            <ul>
                <li><strong>噪音过滤：</strong>移除广告、导航、版权等无关内容</li>
                <li><strong>重复检测：</strong>基于内容相似度的智能去重</li>
                <li><strong>语义提取：</strong>识别标题、段落、列表等结构化内容</li>
                <li><strong>质量评估：</strong>多维度评估数据质量并打分</li>
            </ul>
        </div>

        <div class="feature-card">
            <h3>4. 增强内容提取 <span class="status enhanced">ENHANCED</span></h3>
            <p><strong>功能描述：</strong>升级的页面内容提取算法</p>
            <ul>
                <li><strong>语义化提取：</strong>基于HTML语义标签的智能提取</li>
                <li><strong>结构化输出：</strong>标题层次、段落内容、要点信息分类</li>
                <li><strong>智能过滤：</strong>排除停用词和无意义内容</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 功能对比</h2>
        
        <div class="comparison">
            <div class="before">
                <h4>第一阶段（基础功能）</h4>
                <ul>
                    <li>单页面抓取</li>
                    <li>基础内容提取</li>
                    <li>简单数据融合</li>
                    <li>基础错误处理</li>
                </ul>
            </div>
            <div class="after">
                <h4>第二阶段（扩展功能）</h4>
                <ul>
                    <li><span class="highlight">3层级递进抓取</span></li>
                    <li><span class="highlight">智能链接过滤</span></li>
                    <li><span class="highlight">高级数据清洗</span></li>
                    <li><span class="highlight">质量评估系统</span></li>
                    <li><span class="highlight">语义化内容提取</span></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试用例</h2>
        
        <div class="test-case">
            <h4>测试用例1：技术项目网站</h4>
            <p><strong>输入：</strong> <code>https://github.com/microsoft/vscode</code></p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>第1层级：项目主页信息</li>
                <li>第2层级：README、文档、发布页面</li>
                <li>第3层级：详细技术文档、API说明</li>
                <li>数据质量：高（预期评分 > 70）</li>
            </ul>
        </div>

        <div class="test-case">
            <h4>测试用例2：企业官网</h4>
            <p><strong>输入：</strong> <code>openai.com</code></p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>第1层级：首页概述</li>
                <li>第2层级：产品页面、关于页面、研究页面</li>
                <li>第3层级：具体产品文档、研究论文</li>
                <li>智能过滤：排除登录、联系等页面</li>
            </ul>
        </div>

        <div class="test-case">
            <h4>测试用例3：项目名称搜索</h4>
            <p><strong>输入：</strong> <code>TensorFlow</code></p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>自动搜索并定位官方网站</li>
                <li>执行3层级抓取</li>
                <li>高质量数据整合</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>⚙️ 技术实现细节</h2>
        
        <h3>新增文件结构</h3>
        <div class="code-block">
├── data-processor.js     # 高级数据清洗模块
├── crawler.js           # 增强的3层级爬虫（已更新）
├── content-script.js    # 增强的内容提取（已更新）
├── data-integrator.js   # 增强的数据融合（已更新）
└── stage2-test.html     # 第二阶段测试页面
        </div>

        <h3>关键算法</h3>
        <div class="feature-card">
            <h4>链接优先级评分算法</h4>
            <div class="code-block">
score = 基础分数(10) 
      - 路径深度惩罚(depth × 2)
      + 关键词加分(keyword × 5)
      - 低价值页面惩罚(10)
            </div>
        </div>

        <div class="feature-card">
            <h4>数据质量评估</h4>
            <div class="code-block">
质量分数 = 内容长度分数(30) 
        + 链接数量分数(20)
        + 元数据完整性(25)
        + 多层级数据(15)
        + 其他因素(10)
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 测试检查清单</h2>
        
        <div class="feature-card">
            <h3>功能测试</h3>
            <ul>
                <li>□ 3层级抓取正常执行</li>
                <li>□ 智能链接过滤生效</li>
                <li>□ 数据清洗算法工作</li>
                <li>□ 质量评估准确</li>
                <li>□ 进度显示正确</li>
                <li>□ 错误处理完善</li>
            </ul>
        </div>

        <div class="feature-card">
            <h3>性能测试</h3>
            <ul>
                <li>□ 并发控制有效（最多3个标签页）</li>
                <li>□ 超时控制正常（15秒/页面）</li>
                <li>□ 内存使用合理</li>
                <li>□ 总抓取时间可接受（< 2分钟）</li>
            </ul>
        </div>

        <div class="feature-card">
            <h3>数据质量测试</h3>
            <ul>
                <li>□ 内容去重效果好</li>
                <li>□ 噪音过滤彻底</li>
                <li>□ 结构化提取准确</li>
                <li>□ 质量评分合理</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 开始测试</h2>
        <p>请按照以下步骤测试第二阶段功能：</p>
        <ol>
            <li><strong>重新加载插件：</strong>在浏览器扩展管理页面刷新插件</li>
            <li><strong>测试输入：</strong>使用上述测试用例中的URL或域名</li>
            <li><strong>观察进度：</strong>注意4步进度显示和详细消息</li>
            <li><strong>检查结果：</strong>验证报告质量和数据完整性</li>
            <li><strong>性能监控：</strong>观察浏览器性能和标签页管理</li>
        </ol>
        
        <div class="feature-card">
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li>第二阶段功能会创建更多标签页，请确保浏览器性能充足</li>
                <li>3层级抓取需要更长时间，请耐心等待</li>
                <li>某些网站可能有反爬虫机制，属于正常情况</li>
                <li>数据质量评分会显示在融合数据中</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('第二阶段功能测试页面已加载');
        console.log('新增功能：3层级抓取、智能过滤、高级清洗、质量评估');
    </script>
</body>
</html>
