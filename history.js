// history.js - 历史记录页面逻辑

let allHistory = [];
let filteredHistory = [];

document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadHistory();
});

function setupEventListeners() {
    // 返回按钮
    document.getElementById('backBtn').addEventListener('click', function() {
        window.close();
    });
    
    // 清空按钮
    document.getElementById('clearBtn').addEventListener('click', function() {
        if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
            clearAllHistory();
        }
    });
    
    // 搜索框
    document.getElementById('searchInput').addEventListener('input', function(e) {
        filterHistory(e.target.value);
    });
}

async function loadHistory() {
    try {
        const result = await chrome.storage.local.get(['reportHistory']);
        allHistory = result.reportHistory || [];
        filteredHistory = [...allHistory];
        
        updateStats();
        displayHistory();
    } catch (error) {
        console.error('加载历史记录失败:', error);
        showError('加载历史记录失败');
    }
}

function updateStats() {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const thisMonthCount = allHistory.filter(item => 
        new Date(item.date) >= thisMonth
    ).length;
    
    const thisWeekCount = allHistory.filter(item => 
        new Date(item.date) >= thisWeek
    ).length;
    
    document.getElementById('totalReports').textContent = allHistory.length;
    document.getElementById('thisMonth').textContent = thisMonthCount;
    document.getElementById('thisWeek').textContent = thisWeekCount;
}

function displayHistory() {
    const historyList = document.getElementById('historyList');
    
    if (filteredHistory.length === 0) {
        historyList.innerHTML = `
            <div class="no-history">
                <div class="no-history-icon">📄</div>
                <h3>暂无历史记录</h3>
                <p>开始生成您的第一份项目研究报告吧！</p>
            </div>
        `;
        return;
    }
    
    historyList.innerHTML = filteredHistory.map(item => createHistoryItemHTML(item)).join('');
    
    // 绑定点击事件
    filteredHistory.forEach(item => {
        const element = document.getElementById(`history-${item.id}`);
        if (element) {
            element.addEventListener('click', function(e) {
                if (!e.target.classList.contains('delete-btn')) {
                    openReport(item);
                }
            });
            
            const deleteBtn = element.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    deleteHistoryItem(item.id);
                });
            }
        }
    });
}

function createHistoryItemHTML(item) {
    const date = new Date(item.date);
    const dateString = date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    return `
        <div class="history-item" id="history-${item.id}">
            <div class="history-item-header">
                <div class="history-item-title">${escapeHtml(item.topic)}</div>
                <div class="history-item-date">${dateString}</div>
            </div>
            <div class="history-item-preview">${escapeHtml(item.preview)}</div>
            <button class="delete-btn" title="删除">×</button>
        </div>
    `;
}

function filterHistory(searchTerm) {
    if (!searchTerm.trim()) {
        filteredHistory = [...allHistory];
    } else {
        const term = searchTerm.toLowerCase();
        filteredHistory = allHistory.filter(item => 
            item.topic.toLowerCase().includes(term) ||
            item.preview.toLowerCase().includes(term)
        );
    }
    
    displayHistory();
}

async function openReport(item) {
    try {
        // 将选中的报告数据存储到临时存储中
        await chrome.storage.local.set({ 
            currentReport: {
                topic: item.topic,
                report: item.report,
                date: item.date
            }
        });
        
        // 打开报告页面
        chrome.tabs.create({
            url: chrome.runtime.getURL('report.html')
        });
    } catch (error) {
        console.error('打开报告失败:', error);
        alert('打开报告失败');
    }
}

async function deleteHistoryItem(itemId) {
    try {
        // 从数组中移除项目
        allHistory = allHistory.filter(item => item.id !== itemId);
        filteredHistory = filteredHistory.filter(item => item.id !== itemId);
        
        // 更新存储
        await chrome.storage.local.set({ reportHistory: allHistory });
        
        // 更新显示
        updateStats();
        displayHistory();
        
        // 显示成功消息
        showTemporaryMessage('记录已删除', 'success');
    } catch (error) {
        console.error('删除记录失败:', error);
        showTemporaryMessage('删除失败', 'error');
    }
}

async function clearAllHistory() {
    try {
        allHistory = [];
        filteredHistory = [];
        
        await chrome.storage.local.set({ reportHistory: [] });
        
        updateStats();
        displayHistory();
        
        showTemporaryMessage('所有记录已清空', 'success');
    } catch (error) {
        console.error('清空记录失败:', error);
        showTemporaryMessage('清空失败', 'error');
    }
}

function showError(message) {
    const historyList = document.getElementById('historyList');
    historyList.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #e53e3e;">
            <h3>错误</h3>
            <p>${message}</p>
        </div>
    `;
}

function showTemporaryMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        transition: all 0.3s ease;
        ${type === 'success' ? 'background: #38a169;' : 'background: #e53e3e;'}
    `;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 2000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
