// report.js - 报告页面逻辑

document.addEventListener('DOMContentLoaded', function() {
    loadReport();
    setupEventListeners();
});

function setupEventListeners() {
    // 返回按钮
    document.getElementById('backBtn').addEventListener('click', function() {
        window.close();
    });
    
    // 打印按钮
    document.getElementById('printBtn').addEventListener('click', function() {
        window.print();
    });
    
    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', function() {
        downloadReport();
    });
}

async function loadReport() {
    try {
        const result = await chrome.storage.local.get(['currentReport']);
        const reportData = result.currentReport;
        
        if (!reportData) {
            showError('未找到报告数据');
            return;
        }
        
        displayReport(reportData);
    } catch (error) {
        console.error('加载报告失败:', error);
        showError('加载报告失败');
    }
}

function displayReport(reportData) {
    const { topic, report, date } = reportData;
    
    // 设置日期
    const reportDate = new Date(date);
    const dateString = reportDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    document.getElementById('reportDate').textContent = dateString;
    document.getElementById('footerDate').textContent = dateString;
    
    // 设置项目主题
    document.getElementById('projectTopic').textContent = topic;
    
    // 设置各个部分的内容
    document.getElementById('projectOverview').innerHTML = formatContent(report.overview);
    document.getElementById('researchGoals').innerHTML = formatContent(report.goals);
    document.getElementById('technicalAnalysis').innerHTML = formatContent(report.technical);
    document.getElementById('marketProspects').innerHTML = formatContent(report.market);
    document.getElementById('riskAssessment').innerHTML = formatContent(report.risks);
    document.getElementById('recommendations').innerHTML = formatContent(report.recommendations);
    
    // 设置页面标题
    document.title = `项目研究报告 - ${topic.substring(0, 50)}${topic.length > 50 ? '...' : ''}`;
}

function formatContent(content) {
    if (!content) {
        return '<p class="no-content">暂无内容</p>';
    }
    
    // 将文本转换为HTML格式
    let formattedContent = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(line => {
            // 处理标题
            if (line.startsWith('###')) {
                return `<h4>${line.substring(3).trim()}</h4>`;
            } else if (line.startsWith('##')) {
                return `<h3>${line.substring(2).trim()}</h3>`;
            }
            // 处理列表项
            else if (line.startsWith('- ') || line.startsWith('* ')) {
                return `<li>${line.substring(2).trim()}</li>`;
            }
            // 处理数字列表
            else if (/^\d+\.\s/.test(line)) {
                return `<li>${line.replace(/^\d+\.\s/, '').trim()}</li>`;
            }
            // 处理粗体文本
            else if (line.includes('**')) {
                line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            }
            // 处理斜体文本
            else if (line.includes('*')) {
                line = line.replace(/\*(.*?)\*/g, '<em>$1</em>');
            }
            
            return `<p>${line}</p>`;
        })
        .join('');
    
    // 处理列表
    formattedContent = formattedContent.replace(/(<li>.*?<\/li>)/gs, function(match) {
        if (!match.includes('<ul>') && !match.includes('<ol>')) {
            return `<ul>${match}</ul>`;
        }
        return match;
    });
    
    // 清理连续的列表标签
    formattedContent = formattedContent.replace(/<\/ul>\s*<ul>/g, '');
    formattedContent = formattedContent.replace(/<\/ol>\s*<ol>/g, '');
    
    return formattedContent;
}

function downloadReport() {
    const reportContent = generateReportHTML();
    const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `项目研究报告_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function generateReportHTML() {
    const topic = document.getElementById('projectTopic').textContent;
    const date = document.getElementById('reportDate').textContent;
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目研究报告 - ${topic}</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #667eea; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #667eea; margin-bottom: 10px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-left: 4px solid #667eea; padding-left: 15px; margin-bottom: 15px; }
        .section-content { padding: 15px; background: #f8f9fa; border-radius: 8px; }
        .topic-content { background: #e8f2ff; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        ul, ol { padding-left: 25px; }
        li { margin: 8px 0; }
        p { margin: 10px 0; text-align: justify; }
        strong { color: #333; }
        em { color: #667eea; font-style: normal; font-weight: 500; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #888; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>项目研究报告</h1>
        <p>生成时间：${date}</p>
    </div>
    
    <div class="topic-content">
        <h2>项目主题</h2>
        <p>${topic}</p>
    </div>
    
    <div class="section">
        <h2>📋 项目概述</h2>
        <div class="section-content">${document.getElementById('projectOverview').innerHTML}</div>
    </div>
    
    <div class="section">
        <h2>🎯 研究目标</h2>
        <div class="section-content">${document.getElementById('researchGoals').innerHTML}</div>
    </div>
    
    <div class="section">
        <h2>🔍 技术分析</h2>
        <div class="section-content">${document.getElementById('technicalAnalysis').innerHTML}</div>
    </div>
    
    <div class="section">
        <h2>📊 市场前景</h2>
        <div class="section-content">${document.getElementById('marketProspects').innerHTML}</div>
    </div>
    
    <div class="section">
        <h2>⚠️ 风险评估</h2>
        <div class="section-content">${document.getElementById('riskAssessment').innerHTML}</div>
    </div>
    
    <div class="section">
        <h2>💡 建议与结论</h2>
        <div class="section-content">${document.getElementById('recommendations').innerHTML}</div>
    </div>
    
    <div class="footer">
        <p>本报告由 AI 项目研究报告生成器自动生成</p>
    </div>
</body>
</html>`;
}

function showError(message) {
    document.body.innerHTML = `
        <div style="text-align: center; padding: 50px; color: #e53e3e;">
            <h2>错误</h2>
            <p>${message}</p>
            <button onclick="window.close()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭</button>
        </div>
    `;
}
