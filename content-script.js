// content-script.js - 页面内容提取脚本

class ContentExtractor {
    constructor() {
        this.excludeSelectors = [
            'script', 'style', 'nav', 'header', 'footer', 
            '.advertisement', '.ad', '.ads', '.sidebar',
            '.menu', '.navigation', '.breadcrumb', '.cookie-notice',
            '.social-share', '.comments', '.related-posts'
        ];
        
        this.prioritySelectors = [
            'main', 'article', '.content', '.main-content',
            '.post-content', '.entry-content', '.page-content'
        ];
    }

    /**
     * 提取页面的主要文本内容
     * @returns {string} - 清理后的文本内容
     */
    extractMainContent() {
        try {
            // 首先尝试从优先选择器中提取内容
            let mainContent = this.extractFromPrioritySelectors();
            
            // 如果没有找到主要内容，则从整个body提取
            if (!mainContent || mainContent.length < 100) {
                mainContent = this.extractFromBody();
            }
            
            return this.cleanText(mainContent);
        } catch (error) {
            console.error('内容提取失败:', error);
            return '';
        }
    }

    /**
     * 从优先选择器中提取内容
     * @returns {string} - 提取的文本
     */
    extractFromPrioritySelectors() {
        for (const selector of this.prioritySelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return this.extractTextFromElement(element);
            }
        }
        return '';
    }

    /**
     * 从整个body提取内容
     * @returns {string} - 提取的文本
     */
    extractFromBody() {
        const bodyClone = document.body.cloneNode(true);
        
        // 移除不需要的元素
        this.excludeSelectors.forEach(selector => {
            const elements = bodyClone.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });
        
        return this.extractTextFromElement(bodyClone);
    }

    /**
     * 从元素中提取文本
     * @param {Element} element - DOM元素
     * @returns {string} - 提取的文本
     */
    extractTextFromElement(element) {
        if (!element) return '';
        
        // 获取文本内容，保留基本的段落结构
        let text = '';
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // 跳过隐藏元素的文本
                    const parent = node.parentElement;
                    if (parent) {
                        const style = window.getComputedStyle(parent);
                        if (style.display === 'none' || style.visibility === 'hidden') {
                            return NodeFilter.FILTER_REJECT;
                        }
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );
        
        let node;
        while (node = walker.nextNode()) {
            const textContent = node.textContent.trim();
            if (textContent) {
                text += textContent + ' ';
            }
        }
        
        return text;
    }

    /**
     * 清理和格式化文本
     * @param {string} text - 原始文本
     * @returns {string} - 清理后的文本
     */
    cleanText(text) {
        if (!text) return '';
        
        return text
            // 移除多余的空白字符
            .replace(/\s+/g, ' ')
            // 移除特殊字符和符号
            .replace(/[^\w\s\u4e00-\u9fff.,!?;:()\-]/g, '')
            // 移除过短的片段
            .split('.')
            .filter(sentence => sentence.trim().length > 10)
            .join('.')
            .trim();
    }

    /**
     * 提取页面中的所有有效链接
     * @returns {Array<string>} - 链接数组
     */
    extractLinks() {
        try {
            const links = [];
            const linkElements = document.querySelectorAll('a[href]');
            const currentDomain = window.location.hostname;
            
            linkElements.forEach(link => {
                const href = link.getAttribute('href');
                if (href && this.isValidLink(href, currentDomain)) {
                    const absoluteUrl = this.resolveUrl(href, window.location.href);
                    if (absoluteUrl && !links.includes(absoluteUrl)) {
                        links.push(absoluteUrl);
                    }
                }
            });
            
            return links;
        } catch (error) {
            console.error('链接提取失败:', error);
            return [];
        }
    }

    /**
     * 检查链接是否有效
     * @param {string} href - 链接地址
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否有效
     */
    isValidLink(href, baseDomain) {
        // 排除无效链接类型
        const invalidPrefixes = ['mailto:', 'tel:', 'javascript:', '#'];
        if (invalidPrefixes.some(prefix => href.startsWith(prefix))) {
            return false;
        }
        
        // 排除文件下载链接
        const fileExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.jpg', '.png', '.gif'];
        if (fileExtensions.some(ext => href.toLowerCase().includes(ext))) {
            return false;
        }
        
        // 如果是绝对链接，检查域名关联性
        if (href.startsWith('http')) {
            try {
                const linkDomain = new URL(href).hostname;
                return this.isRelatedDomain(linkDomain, baseDomain);
            } catch (error) {
                return false;
            }
        }
        
        // 相对链接视为有效
        return true;
    }

    /**
     * 检查域名是否相关
     * @param {string} linkDomain - 链接域名
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否相关
     */
    isRelatedDomain(linkDomain, baseDomain) {
        // 完全相同
        if (linkDomain === baseDomain) {
            return true;
        }
        
        // 子域名关系
        const baseMainDomain = this.getMainDomain(baseDomain);
        const linkMainDomain = this.getMainDomain(linkDomain);
        
        return baseMainDomain === linkMainDomain;
    }

    /**
     * 获取主域名
     * @param {string} domain - 完整域名
     * @returns {string} - 主域名
     */
    getMainDomain(domain) {
        const parts = domain.split('.');
        if (parts.length >= 2) {
            return parts.slice(-2).join('.');
        }
        return domain;
    }

    /**
     * 解析相对URL为绝对URL
     * @param {string} relativeUrl - 相对URL
     * @param {string} baseUrl - 基础URL
     * @returns {string} - 绝对URL
     */
    resolveUrl(relativeUrl, baseUrl) {
        try {
            return new URL(relativeUrl, baseUrl).href;
        } catch (error) {
            return '';
        }
    }

    /**
     * 提取页面元数据
     * @returns {Object} - 页面元数据
     */
    extractMetadata() {
        return {
            title: document.title || '',
            description: this.getMetaContent('description') || '',
            keywords: this.getMetaContent('keywords') || '',
            url: window.location.href,
            domain: window.location.hostname
        };
    }

    /**
     * 获取meta标签内容
     * @param {string} name - meta标签名称
     * @returns {string} - meta内容
     */
    getMetaContent(name) {
        const meta = document.querySelector(`meta[name="${name}"]`) || 
                    document.querySelector(`meta[property="og:${name}"]`);
        return meta ? meta.getAttribute('content') || '' : '';
    }
}

// 监听来自插件的消息
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'extractContent') {
            const extractor = new ContentExtractor();
            
            const result = {
                content: extractor.extractMainContent(),
                links: extractor.extractLinks(),
                metadata: extractor.extractMetadata()
            };
            
            sendResponse(result);
        }
    });
}

// 如果在普通网页环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.ContentExtractor = ContentExtractor;
}
