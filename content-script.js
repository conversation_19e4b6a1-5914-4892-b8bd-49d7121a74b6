// content-script.js - 页面内容提取脚本

class ContentExtractor {
    constructor() {
        this.excludeSelectors = [
            'script', 'style', 'nav', 'header', 'footer',
            '.advertisement', '.ad', '.ads', '.sidebar',
            '.menu', '.navigation', '.breadcrumb', '.cookie-notice',
            '.social-share', '.comments', '.related-posts',
            '.popup', '.modal', '.overlay', '.banner', '.promo',
            '.newsletter', '.subscription', '.social-media',
            '.pagination', '.tags', '.categories', '.author-bio'
        ];

        this.prioritySelectors = [
            'main', 'article', '.content', '.main-content',
            '.post-content', '.entry-content', '.page-content',
            '.article-content', '.text-content', '.body-content',
            '[role="main"]', '.primary-content', '.main-text'
        ];

        this.semanticSelectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'div', 'section', 'article',
            '.description', '.summary', '.abstract',
            '.intro', '.overview', '.details'
        ];
    }

    /**
     * 提取页面的主要文本内容（增强版）
     * @returns {string} - 清理后的文本内容
     */
    extractMainContent() {
        try {
            // 1. 尝试语义化内容提取
            let mainContent = this.extractSemanticContent();

            // 2. 如果语义化提取不够，尝试优先选择器
            if (!mainContent || mainContent.length < 100) {
                mainContent = this.extractFromPrioritySelectors();
            }

            // 3. 最后尝试从整个body提取
            if (!mainContent || mainContent.length < 100) {
                mainContent = this.extractFromBody();
            }

            // 4. 智能清理和结构化
            return this.intelligentCleanText(mainContent);
        } catch (error) {
            console.error('内容提取失败:', error);
            return '';
        }
    }

    /**
     * 语义化内容提取
     * @returns {string} - 提取的内容
     */
    extractSemanticContent() {
        const contentParts = [];

        // 提取标题层次结构
        const headings = this.extractHeadings();
        if (headings.length > 0) {
            contentParts.push('=== 页面结构 ===');
            contentParts.push(headings.join('\n'));
            contentParts.push('');
        }

        // 提取主要段落内容
        const paragraphs = this.extractMeaningfulParagraphs();
        if (paragraphs.length > 0) {
            contentParts.push('=== 主要内容 ===');
            contentParts.push(paragraphs.join('\n\n'));
            contentParts.push('');
        }

        // 提取列表内容
        const lists = this.extractLists();
        if (lists.length > 0) {
            contentParts.push('=== 要点信息 ===');
            contentParts.push(lists.join('\n'));
        }

        return contentParts.join('\n');
    }

    /**
     * 提取标题层次结构
     * @returns {Array<string>} - 标题数组
     */
    extractHeadings() {
        const headings = [];
        const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

        headingElements.forEach(heading => {
            const text = heading.textContent.trim();
            if (text && text.length > 3 && text.length < 100) {
                const level = parseInt(heading.tagName.substring(1));
                const indent = '  '.repeat(level - 1);
                headings.push(`${indent}${text}`);
            }
        });

        return headings.slice(0, 20); // 限制标题数量
    }

    /**
     * 提取有意义的段落
     * @returns {Array<string>} - 段落数组
     */
    extractMeaningfulParagraphs() {
        const paragraphs = [];
        const paragraphElements = document.querySelectorAll('p, .description, .summary, .intro');

        paragraphElements.forEach(p => {
            const text = p.textContent.trim();
            if (this.isMeaningfulParagraph(text)) {
                paragraphs.push(text);
            }
        });

        return paragraphs.slice(0, 15); // 限制段落数量
    }

    /**
     * 判断段落是否有意义
     * @param {string} text - 段落文本
     * @returns {boolean} - 是否有意义
     */
    isMeaningfulParagraph(text) {
        if (!text || text.length < 30 || text.length > 500) {
            return false;
        }

        // 排除导航和菜单文本
        const navPatterns = [
            /^(home|about|contact|login|register|menu|search)/i,
            /^(首页|关于|联系|登录|注册|菜单|搜索)/,
            /click here|read more|learn more/i,
            /点击这里|了解更多|查看更多/
        ];

        if (navPatterns.some(pattern => pattern.test(text))) {
            return false;
        }

        // 检查是否包含有意义的词汇
        const meaningfulWordCount = this.countMeaningfulWords(text);
        return meaningfulWordCount >= 5;
    }

    /**
     * 计算有意义的词汇数量
     * @param {string} text - 文本
     * @returns {number} - 有意义词汇数量
     */
    countMeaningfulWords(text) {
        const words = text.split(/\s+/);
        const meaningfulWords = words.filter(word => {
            const cleanWord = word.replace(/[^\w\u4e00-\u9fff]/g, '').toLowerCase();
            return cleanWord.length > 2 && !this.isStopWord(cleanWord);
        });
        return meaningfulWords.length;
    }

    /**
     * 检查是否为停用词
     * @param {string} word - 词汇
     * @returns {boolean} - 是否为停用词
     */
    isStopWord(word) {
        const stopWords = new Set([
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '上', '也', '很', '到'
        ]);
        return stopWords.has(word);
    }

    /**
     * 提取列表内容
     * @returns {Array<string>} - 列表项数组
     */
    extractLists() {
        const listItems = [];
        const listElements = document.querySelectorAll('ul li, ol li');

        listElements.forEach(li => {
            const text = li.textContent.trim();
            if (text && text.length > 10 && text.length < 200) {
                listItems.push(`• ${text}`);
            }
        });

        return listItems.slice(0, 20); // 限制列表项数量
    }

    /**
     * 智能文本清理
     * @param {string} text - 原始文本
     * @returns {string} - 清理后的文本
     */
    intelligentCleanText(text) {
        if (!text) return '';

        return text
            // 标准化空白字符
            .replace(/\s+/g, ' ')
            // 移除多余的换行
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            // 移除特殊字符（保留中文和基本标点）
            .replace(/[^\w\s\u4e00-\u9fff.,!?;:()\-=]/g, '')
            // 移除过短的行
            .split('\n')
            .filter(line => line.trim().length > 5)
            .join('\n')
            .trim();
    }

    /**
     * 从优先选择器中提取内容
     * @returns {string} - 提取的文本
     */
    extractFromPrioritySelectors() {
        for (const selector of this.prioritySelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return this.extractTextFromElement(element);
            }
        }
        return '';
    }

    /**
     * 从整个body提取内容
     * @returns {string} - 提取的文本
     */
    extractFromBody() {
        const bodyClone = document.body.cloneNode(true);
        
        // 移除不需要的元素
        this.excludeSelectors.forEach(selector => {
            const elements = bodyClone.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });
        
        return this.extractTextFromElement(bodyClone);
    }

    /**
     * 从元素中提取文本
     * @param {Element} element - DOM元素
     * @returns {string} - 提取的文本
     */
    extractTextFromElement(element) {
        if (!element) return '';
        
        // 获取文本内容，保留基本的段落结构
        let text = '';
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // 跳过隐藏元素的文本
                    const parent = node.parentElement;
                    if (parent) {
                        const style = window.getComputedStyle(parent);
                        if (style.display === 'none' || style.visibility === 'hidden') {
                            return NodeFilter.FILTER_REJECT;
                        }
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );
        
        let node;
        while (node = walker.nextNode()) {
            const textContent = node.textContent.trim();
            if (textContent) {
                text += textContent + ' ';
            }
        }
        
        return text;
    }

    /**
     * 清理和格式化文本
     * @param {string} text - 原始文本
     * @returns {string} - 清理后的文本
     */
    cleanText(text) {
        if (!text) return '';
        
        return text
            // 移除多余的空白字符
            .replace(/\s+/g, ' ')
            // 移除特殊字符和符号
            .replace(/[^\w\s\u4e00-\u9fff.,!?;:()\-]/g, '')
            // 移除过短的片段
            .split('.')
            .filter(sentence => sentence.trim().length > 10)
            .join('.')
            .trim();
    }

    /**
     * 提取页面中的所有有效链接
     * @returns {Array<string>} - 链接数组
     */
    extractLinks() {
        try {
            const links = [];
            const linkElements = document.querySelectorAll('a[href]');
            const currentDomain = window.location.hostname;
            
            linkElements.forEach(link => {
                const href = link.getAttribute('href');
                if (href && this.isValidLink(href, currentDomain)) {
                    const absoluteUrl = this.resolveUrl(href, window.location.href);
                    if (absoluteUrl && !links.includes(absoluteUrl)) {
                        links.push(absoluteUrl);
                    }
                }
            });
            
            return links;
        } catch (error) {
            console.error('链接提取失败:', error);
            return [];
        }
    }

    /**
     * 检查链接是否有效
     * @param {string} href - 链接地址
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否有效
     */
    isValidLink(href, baseDomain) {
        // 排除无效链接类型
        const invalidPrefixes = ['mailto:', 'tel:', 'javascript:', '#'];
        if (invalidPrefixes.some(prefix => href.startsWith(prefix))) {
            return false;
        }
        
        // 排除文件下载链接
        const fileExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.jpg', '.png', '.gif'];
        if (fileExtensions.some(ext => href.toLowerCase().includes(ext))) {
            return false;
        }
        
        // 如果是绝对链接，检查域名关联性
        if (href.startsWith('http')) {
            try {
                const linkDomain = new URL(href).hostname;
                return this.isRelatedDomain(linkDomain, baseDomain);
            } catch (error) {
                return false;
            }
        }
        
        // 相对链接视为有效
        return true;
    }

    /**
     * 检查域名是否相关
     * @param {string} linkDomain - 链接域名
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否相关
     */
    isRelatedDomain(linkDomain, baseDomain) {
        // 完全相同
        if (linkDomain === baseDomain) {
            return true;
        }
        
        // 子域名关系
        const baseMainDomain = this.getMainDomain(baseDomain);
        const linkMainDomain = this.getMainDomain(linkDomain);
        
        return baseMainDomain === linkMainDomain;
    }

    /**
     * 获取主域名
     * @param {string} domain - 完整域名
     * @returns {string} - 主域名
     */
    getMainDomain(domain) {
        const parts = domain.split('.');
        if (parts.length >= 2) {
            return parts.slice(-2).join('.');
        }
        return domain;
    }

    /**
     * 解析相对URL为绝对URL
     * @param {string} relativeUrl - 相对URL
     * @param {string} baseUrl - 基础URL
     * @returns {string} - 绝对URL
     */
    resolveUrl(relativeUrl, baseUrl) {
        try {
            return new URL(relativeUrl, baseUrl).href;
        } catch (error) {
            return '';
        }
    }

    /**
     * 提取页面元数据
     * @returns {Object} - 页面元数据
     */
    extractMetadata() {
        return {
            title: document.title || '',
            description: this.getMetaContent('description') || '',
            keywords: this.getMetaContent('keywords') || '',
            url: window.location.href,
            domain: window.location.hostname
        };
    }

    /**
     * 获取meta标签内容
     * @param {string} name - meta标签名称
     * @returns {string} - meta内容
     */
    getMetaContent(name) {
        const meta = document.querySelector(`meta[name="${name}"]`) || 
                    document.querySelector(`meta[property="og:${name}"]`);
        return meta ? meta.getAttribute('content') || '' : '';
    }
}

// 监听来自插件的消息
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'extractContent') {
            const extractor = new ContentExtractor();
            
            const result = {
                content: extractor.extractMainContent(),
                links: extractor.extractLinks(),
                metadata: extractor.extractMetadata()
            };
            
            sendResponse(result);
        }
    });
}

// 如果在普通网页环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.ContentExtractor = ContentExtractor;
}
