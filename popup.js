// popup.js - 主要的弹出窗口逻辑

// Gemini API 配置
const GEMINI_API_KEY = 'AIzaSyA3Qpikt4WprxuuRlEws_NKtaTohVxpQCY';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

// DOM 元素
let projectInput, generateBtn, historyBtn, charCount, errorMessage, successMessage;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    updateCharCount();
});

function initializeElements() {
    projectInput = document.getElementById('projectInput');
    generateBtn = document.getElementById('generateBtn');
    historyBtn = document.getElementById('historyBtn');
    charCount = document.getElementById('charCount');
    errorMessage = document.getElementById('errorMessage');
    successMessage = document.getElementById('successMessage');
}

function setupEventListeners() {
    // 输入框字符计数
    projectInput.addEventListener('input', updateCharCount);
    
    // 生成报告按钮
    generateBtn.addEventListener('click', handleGenerateReport);
    
    // 历史记录按钮
    historyBtn.addEventListener('click', showHistory);
    
    // 回车键快捷生成
    projectInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            handleGenerateReport();
        }
    });
}

function updateCharCount() {
    const count = projectInput.value.length;
    charCount.textContent = count;
    
    if (count > 450) {
        charCount.style.color = '#e53e3e';
    } else if (count > 350) {
        charCount.style.color = '#dd6b20';
    } else {
        charCount.style.color = '#888';
    }
}

async function handleGenerateReport() {
    const topic = projectInput.value.trim();

    if (!topic) {
        showError('请输入项目主题');
        return;
    }

    if (topic.length < 10) {
        showError('项目主题描述过短，请提供更详细的信息');
        return;
    }

    setLoadingState(true);
    hideMessages();

    try {
        // 并行收集AI数据和爬虫数据
        const [aiData, crawlerData] = await Promise.allSettled([
            generateAIReport(topic),
            collectCrawlerData(topic)
        ]);

        // 处理结果
        const aiResult = aiData.status === 'fulfilled' ? aiData.value : null;
        const crawlerResult = crawlerData.status === 'fulfilled' ? crawlerData.value : null;

        // 整合数据
        const integratedReport = dataIntegrator.integrateData(aiResult, crawlerResult, topic);

        await saveReportToHistory(topic, integratedReport);
        showReport(topic, integratedReport);
        showSuccess('报告生成成功！');
    } catch (error) {
        console.error('生成报告失败:', error);
        showError('生成报告失败: ' + error.message);
    } finally {
        setLoadingState(false);
    }
}

async function generateAIReport(topic) {
    const prompt = createPrompt(topic);

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('API返回数据格式错误');
    }

    const reportText = data.candidates[0].content.parts[0].text;
    return parseReport(reportText);
}

async function collectCrawlerData(topic) {
    try {
        // 更新加载状态显示爬虫进度
        updateLoadingMessage('正在分析输入类型...');

        const crawlerResult = await webCrawler.startCrawling(topic, (progress) => {
            updateLoadingMessage(progress.message);
        });

        return crawlerResult;
    } catch (error) {
        console.error('爬虫数据收集失败:', error);
        // 爬虫失败不影响整体流程，返回空结果
        return null;
    }
}

function createPrompt(topic) {
    return `请为以下项目主题生成一份详细的研究报告：

项目主题：${topic}

请按照以下结构生成报告，每个部分用"##"标记开始：

## 项目概述
简要介绍项目的背景、目的和意义

## 研究目标
明确列出项目的主要研究目标和预期成果

## 技术分析
分析项目涉及的关键技术、技术难点和解决方案

## 市场前景
评估项目的市场潜力、目标用户和商业价值

## 风险评估
识别项目可能面临的技术风险、市场风险和其他挑战

## 建议与结论
提供具体的实施建议和总结性结论

请确保内容专业、详细且具有实用性。每个部分应包含具体的分析和建议。`;
}

function parseReport(reportText) {
    const sections = {
        overview: '',
        goals: '',
        technical: '',
        market: '',
        risks: '',
        recommendations: ''
    };
    
    const lines = reportText.split('\n');
    let currentSection = '';
    let content = [];
    
    for (const line of lines) {
        if (line.startsWith('## ')) {
            // 保存前一个部分的内容
            if (currentSection && content.length > 0) {
                sections[currentSection] = content.join('\n').trim();
                content = [];
            }
            
            // 确定当前部分
            const sectionTitle = line.substring(3).trim();
            if (sectionTitle.includes('概述')) {
                currentSection = 'overview';
            } else if (sectionTitle.includes('目标')) {
                currentSection = 'goals';
            } else if (sectionTitle.includes('技术')) {
                currentSection = 'technical';
            } else if (sectionTitle.includes('市场')) {
                currentSection = 'market';
            } else if (sectionTitle.includes('风险')) {
                currentSection = 'risks';
            } else if (sectionTitle.includes('建议') || sectionTitle.includes('结论')) {
                currentSection = 'recommendations';
            }
        } else if (currentSection && line.trim()) {
            content.push(line);
        }
    }
    
    // 保存最后一个部分
    if (currentSection && content.length > 0) {
        sections[currentSection] = content.join('\n').trim();
    }
    
    return sections;
}

async function saveReportToHistory(topic, report) {
    const historyItem = {
        id: Date.now().toString(),
        topic: topic,
        report: report,
        date: new Date().toISOString(),
        preview: topic.substring(0, 100) + (topic.length > 100 ? '...' : '')
    };
    
    const history = await getHistory();
    history.unshift(historyItem);
    
    // 限制历史记录数量
    if (history.length > 50) {
        history.splice(50);
    }
    
    await chrome.storage.local.set({ reportHistory: history });
}

async function getHistory() {
    const result = await chrome.storage.local.get(['reportHistory']);
    return result.reportHistory || [];
}

function showReport(topic, report) {
    const reportData = {
        topic: topic,
        report: report,
        date: new Date().toISOString()
    };
    
    // 将报告数据存储到临时存储中
    chrome.storage.local.set({ currentReport: reportData });
    
    // 打开报告页面
    chrome.tabs.create({
        url: chrome.runtime.getURL('report.html')
    });
}

function showHistory() {
    chrome.tabs.create({
        url: chrome.runtime.getURL('history.html')
    });
}

function setLoadingState(loading) {
    const btnText = generateBtn.querySelector('.btn-text');
    const spinner = generateBtn.querySelector('.loading-spinner');

    if (loading) {
        generateBtn.disabled = true;
        btnText.textContent = '生成中...';
        spinner.style.display = 'inline-block';
    } else {
        generateBtn.disabled = false;
        btnText.textContent = '生成报告';
        spinner.style.display = 'none';
    }
}

function updateLoadingMessage(message) {
    const btnText = generateBtn.querySelector('.btn-text');
    if (btnText && generateBtn.disabled) {
        btnText.textContent = message;
    }
}

function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
}

function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
}

function hideMessages() {
    errorMessage.style.display = 'none';
    successMessage.style.display = 'none';
}
