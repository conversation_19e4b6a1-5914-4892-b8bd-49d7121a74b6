# AI项目研究报告生成器

一个基于Gemini AI的浏览器插件，能够自动生成专业的项目研究报告。

## 功能特性

- 🤖 **AI驱动**: 使用Google Gemini AI生成高质量的项目研究报告
- 📝 **智能分析**: 自动分析项目概述、研究目标、技术分析、市场前景等
- 📚 **历史记录**: 保存和管理所有生成的报告
- 🎨 **美观界面**: 现代化的用户界面设计
- 💾 **导出功能**: 支持打印和下载HTML格式报告
- 🔍 **搜索功能**: 快速搜索历史报告

## 安装方法

### Chrome浏览器
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹
6. 插件安装完成

### Edge浏览器
1. 打开Edge浏览器
2. 访问 `edge://extensions/`
3. 开启左下角的"开发人员模式"
4. 点击"加载解压缩的扩展"
5. 选择本项目文件夹
6. 插件安装完成

## 使用方法

### 生成报告
1. 点击浏览器工具栏中的插件图标
2. 在输入框中输入项目主题或描述
3. 点击"生成报告"按钮
4. 等待AI生成完成，报告将在新标签页中打开

### 查看历史记录
1. 在插件弹窗中点击"查看历史记录"
2. 浏览所有已生成的报告
3. 点击任意报告可重新查看
4. 使用搜索框快速查找特定报告

### 报告管理
- **打印报告**: 在报告页面点击"打印报告"按钮
- **保存报告**: 点击"保存报告"下载HTML文件
- **删除记录**: 在历史记录页面悬停并点击删除按钮

## 技术架构

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **AI服务**: Google Gemini 2.0 Flash API
- **存储**: Chrome Extension Storage API
- **架构**: Manifest V3

## 文件结构

```
├── manifest.json          # 插件配置文件
├── popup.html             # 主界面HTML
├── popup.js               # 主界面逻辑
├── styles.css             # 主界面样式
├── report.html            # 报告页面HTML
├── report.js              # 报告页面逻辑
├── report-styles.css      # 报告页面样式
├── history.html           # 历史记录页面HTML
├── history.js             # 历史记录页面逻辑
├── icons/                 # 插件图标
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

## API配置

本插件使用Google Gemini API，API密钥已内置在代码中。如需更换API密钥，请修改 `popup.js` 文件中的 `GEMINI_API_KEY` 常量。

## 安全特性

- ✅ 输入验证和XSS防护
- ✅ 内容安全策略(CSP)
- ✅ 安全的API调用
- ✅ 本地数据存储

## 浏览器兼容性

- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ 其他基于Chromium的浏览器

## 使用限制

- 项目主题输入限制为500字符
- 历史记录最多保存50条
- 需要网络连接以访问Gemini API

## 故障排除

### 常见问题

**Q: 插件无法生成报告**
A: 请检查网络连接，确保能够访问Google服务

**Q: 历史记录丢失**
A: 历史记录存储在浏览器本地，清除浏览器数据可能导致丢失

**Q: 报告内容不完整**
A: 请尝试提供更详细的项目描述，或重新生成报告

### 错误代码
- `API请求失败`: 网络连接问题或API服务不可用
- `API返回数据格式错误`: API响应异常
- `未找到报告数据`: 报告数据丢失或损坏

## 开发说明

如需修改或扩展功能，请注意：

1. 修改API密钥需要重新加载插件
2. 样式修改后需要刷新页面
3. 添加新功能需要更新manifest.json权限

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础报告生成功能
- 历史记录管理
- 美观的用户界面
