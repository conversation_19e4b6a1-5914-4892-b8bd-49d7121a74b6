<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件尺寸测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .size-display {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        
        .ratio-info {
            background: #f0fff4;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #38a169;
        }
        
        .center-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🔧 插件尺寸测试页面</h1>
        <p>此页面用于测试调整后的插件弹出窗口尺寸效果。</p>
        
        <div class="size-display">
            <h3>📏 当前尺寸设置</h3>
            <ul>
                <li><strong>宽度:</strong> 600px</li>
                <li><strong>高度:</strong> 480px</li>
                <li><strong>长宽比:</strong> 600:480 = 5:4 (1.25:1)</li>
            </ul>
        </div>
        
        <div class="ratio-info">
            <h3>✅ 比例验证</h3>
            <p>目标比例: 5:4 = 1.25</p>
            <p>实际比例: 600 ÷ 480 = 1.25 ✓</p>
            <p>比例匹配: <strong style="color: #38a169;">完全符合要求</strong></p>
        </div>
        
        <h3>🎯 测试说明</h3>
        <ol>
            <li>下方显示的是实际的插件弹出窗口</li>
            <li>请检查内容是否完整显示</li>
            <li>测试各个功能按钮是否正常工作</li>
            <li>验证在不同屏幕尺寸下的响应式效果</li>
        </ol>
    </div>
    
    <div class="center-container">
        <!-- 这里是实际的插件内容 -->
        <div class="container">
            <div class="header">
                <img src="icons/icon32.png" alt="AI报告生成器" class="logo">
                <h1>AI项目研究报告生成器</h1>
            </div>
            
            <div class="main-content">
                <div class="input-section">
                    <label for="projectInput">项目主题</label>
                    <textarea 
                        id="projectInput" 
                        placeholder="请输入项目主题或描述，例如：人工智能在医疗诊断中的应用..." 
                        rows="4"
                        maxlength="500"
                    ></textarea>
                    <div class="char-count">
                        <span id="charCount">0</span>/500
                    </div>
                </div>
                
                <div class="button-section">
                    <button id="generateBtn" class="primary-btn">
                        <span class="btn-text">生成报告</span>
                        <div class="loading-spinner" style="display: none;"></div>
                    </button>
                    <button id="historyBtn" class="secondary-btn">查看历史记录</button>
                </div>
                
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <div id="successMessage" class="success-message" style="display: none;"></div>
            </div>
            
            <div class="footer">
                <p>由 Gemini AI 提供技术支持</p>
            </div>
        </div>
    </div>
    
    <div class="test-info" style="margin-top: 20px;">
        <h3>📱 响应式测试</h3>
        <p>请尝试调整浏览器窗口大小，测试响应式效果：</p>
        <ul>
            <li><strong>大屏幕 (>650px):</strong> 固定600x480尺寸，水平按钮布局</li>
            <li><strong>小屏幕 (≤650px):</strong> 全宽显示，垂直按钮布局</li>
        </ul>
        
        <h3>🔍 检查清单</h3>
        <ul>
            <li>□ 容器尺寸为600x480像素</li>
            <li>□ 长宽比为5:4</li>
            <li>□ 所有内容完整显示</li>
            <li>□ 按钮布局合理</li>
            <li>□ 文本输入框大小适中</li>
            <li>□ 响应式设计正常工作</li>
            <li>□ 样式美观，无布局错乱</li>
        </ul>
    </div>
    
    <script>
        // 简单的字符计数功能
        const projectInput = document.getElementById('projectInput');
        const charCount = document.getElementById('charCount');
        
        if (projectInput && charCount) {
            projectInput.addEventListener('input', function() {
                const count = this.value.length;
                charCount.textContent = count;
                
                if (count > 450) {
                    charCount.style.color = '#e53e3e';
                } else if (count > 350) {
                    charCount.style.color = '#dd6b20';
                } else {
                    charCount.style.color = '#888';
                }
            });
        }
        
        // 显示当前窗口尺寸
        function showWindowSize() {
            console.log(`窗口尺寸: ${window.innerWidth} x ${window.innerHeight}`);
        }
        
        window.addEventListener('resize', showWindowSize);
        showWindowSize();
        
        // 测试按钮点击
        document.getElementById('generateBtn').addEventListener('click', function() {
            alert('生成报告功能测试 - 尺寸调整完成！');
        });
        
        document.getElementById('historyBtn').addEventListener('click', function() {
            alert('历史记录功能测试 - 尺寸调整完成！');
        });
    </script>
</body>
</html>
