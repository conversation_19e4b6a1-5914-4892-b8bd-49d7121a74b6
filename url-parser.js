// url-parser.js - URL解析和输入类型识别工具

class InputParser {
    constructor() {
        this.urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
        this.domainRegex = /^[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}$/;
    }

    /**
     * 识别用户输入的类型
     * @param {string} input - 用户输入
     * @returns {Object} - {type: 'url'|'domain'|'project', value: string, normalizedUrl?: string}
     */
    identifyInputType(input) {
        const trimmedInput = input.trim();
        
        // 检查是否为完整URL
        if (this.urlRegex.test(trimmedInput)) {
            return {
                type: 'url',
                value: trimmedInput,
                normalizedUrl: trimmedInput
            };
        }
        
        // 检查是否为域名
        if (this.domainRegex.test(trimmedInput)) {
            const normalizedUrl = this.normalizeDomain(trimmedInput);
            return {
                type: 'domain',
                value: trimmedInput,
                normalizedUrl: normalizedUrl
            };
        }
        
        // 其他情况视为项目名称
        return {
            type: 'project',
            value: trimmedInput
        };
    }

    /**
     * 规范化域名为完整URL
     * @param {string} domain - 域名
     * @returns {string} - 完整URL
     */
    normalizeDomain(domain) {
        // 移除可能的协议前缀
        domain = domain.replace(/^https?:\/\//, '');
        
        // 添加https协议
        return `https://${domain}`;
    }

    /**
     * 验证URL是否可访问
     * @param {string} url - 要验证的URL
     * @returns {Promise<boolean>} - 是否可访问
     */
    async validateUrl(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors'
            });
            return true;
        } catch (error) {
            console.log(`URL验证失败: ${url}`, error);
            return false;
        }
    }

    /**
     * 从项目名称生成搜索URL
     * @param {string} projectName - 项目名称
     * @returns {string} - 搜索URL
     */
    generateSearchUrl(projectName) {
        const encodedQuery = encodeURIComponent(`${projectName} 项目 官网`);
        return `https://www.google.com/search?q=${encodedQuery}`;
    }

    /**
     * 提取域名
     * @param {string} url - 完整URL
     * @returns {string} - 域名
     */
    extractDomain(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch (error) {
            console.error('提取域名失败:', error);
            return '';
        }
    }

    /**
     * 检查URL是否为同域名或相关域名
     * @param {string} url - 要检查的URL
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否相关
     */
    isRelatedDomain(url, baseDomain) {
        try {
            const urlDomain = this.extractDomain(url);
            const baseMainDomain = this.getMainDomain(baseDomain);
            const urlMainDomain = this.getMainDomain(urlDomain);
            
            // 完全相同的域名
            if (urlDomain === baseDomain) {
                return true;
            }
            
            // 相同的主域名（如 blog.example.com 和 www.example.com）
            if (urlMainDomain === baseMainDomain) {
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('域名关联检查失败:', error);
            return false;
        }
    }

    /**
     * 获取主域名（去除子域名）
     * @param {string} domain - 完整域名
     * @returns {string} - 主域名
     */
    getMainDomain(domain) {
        const parts = domain.split('.');
        if (parts.length >= 2) {
            return parts.slice(-2).join('.');
        }
        return domain;
    }

    /**
     * 过滤无效链接
     * @param {string} url - 要检查的URL
     * @param {string} baseDomain - 基础域名
     * @returns {boolean} - 是否为有效链接
     */
    isValidLink(url, baseDomain) {
        try {
            // 排除邮件链接
            if (url.startsWith('mailto:')) {
                return false;
            }
            
            // 排除电话链接
            if (url.startsWith('tel:')) {
                return false;
            }
            
            // 排除锚点链接
            if (url.startsWith('#')) {
                return false;
            }
            
            // 排除JavaScript链接
            if (url.startsWith('javascript:')) {
                return false;
            }
            
            // 排除文件下载链接
            const fileExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar'];
            if (fileExtensions.some(ext => url.toLowerCase().includes(ext))) {
                return false;
            }
            
            // 检查是否为相关域名
            if (url.startsWith('http')) {
                return this.isRelatedDomain(url, baseDomain);
            }
            
            // 相对链接视为有效
            return true;
        } catch (error) {
            console.error('链接验证失败:', error);
            return false;
        }
    }

    /**
     * 将相对URL转换为绝对URL
     * @param {string} relativeUrl - 相对URL
     * @param {string} baseUrl - 基础URL
     * @returns {string} - 绝对URL
     */
    resolveUrl(relativeUrl, baseUrl) {
        try {
            return new URL(relativeUrl, baseUrl).href;
        } catch (error) {
            console.error('URL解析失败:', error);
            return '';
        }
    }
}

// 导出实例
const inputParser = new InputParser();

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.inputParser = inputParser;
}
