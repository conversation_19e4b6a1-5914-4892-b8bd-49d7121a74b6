/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-width: 320px;
    width: auto;
}

.container {
    width: 320px;
    height: 400px;
    min-width: 300px;
    max-width: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    box-sizing: border-box;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 主要内容区域 */
.main-content {
    padding: 20px;
}

.input-section {
    margin-bottom: 25px;
}

.input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

#projectInput {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

#projectInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-count {
    text-align: right;
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

/* 按钮样式 */
.button-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.primary-btn, .secondary-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.primary-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.primary-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.secondary-btn {
    background: #f8f9fa;
    color: #667eea;
    border: 2px solid #667eea;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息样式 */
.error-message, .success-message {
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 15px;
}

.error-message {
    background: #fee;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.success-message {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #c6f6d5;
}

/* 底部样式 */
.footer {
    background: #f8f9fa;
    padding: 15px;
    text-align: center;
    border-top: 1px solid #e1e5e9;
}

.footer p {
    font-size: 12px;
    color: #888;
    margin: 0;
}

/* 历史记录页面样式 */
.history-container {
    padding: 20px;
}

.history-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e5e9;
}

.back-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-right: 15px;
    color: #667eea;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background: #f0f0f0;
}

.history-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.history-item-date {
    font-size: 12px;
    color: #888;
    margin-bottom: 8px;
}

.history-item-preview {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.no-history {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 40px 20px;
}

/* 进度显示样式 */
.progress-container {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: #e1e5e9;
    z-index: 1;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e1e5e9;
    color: #888;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.step-text {
    font-size: 11px;
    color: #666;
    text-align: center;
    font-weight: 500;
}

.progress-step.active .step-icon {
    background: #667eea;
    color: white;
    animation: pulse 1.5s infinite;
}

.progress-step.completed .step-icon {
    background: #38a169;
    color: white;
}

.progress-step.completed:not(:last-child)::after {
    background: #38a169;
}

.progress-message {
    text-align: center;
    font-size: 13px;
    color: #667eea;
    font-weight: 500;
    min-height: 20px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 响应式设计 */
@media (max-width: 350px) {
    .container {
        width: 100%;
        min-width: 300px;
        height: auto;
        min-height: 400px;
        border-radius: 0;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }

    .progress-step {
        flex: 0 0 45%;
    }

    .progress-step:not(:last-child)::after {
        display: none;
    }
}
