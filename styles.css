/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    width: 400px;
    height: 320px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    box-sizing: border-box;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 主要内容区域 */
.main-content {
    padding: 20px;
}

.input-section {
    margin-bottom: 15px;
}

.input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

#projectInput {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

#projectInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-count {
    text-align: right;
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

/* 按钮样式 */
.button-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.primary-btn, .secondary-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.primary-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.primary-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.secondary-btn {
    background: #f8f9fa;
    color: #667eea;
    border: 2px solid #667eea;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息样式 */
.error-message, .success-message {
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 15px;
}

.error-message {
    background: #fee;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.success-message {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #c6f6d5;
}

/* 底部样式 */
.footer {
    background: #f8f9fa;
    padding: 15px;
    text-align: center;
    border-top: 1px solid #e1e5e9;
}

.footer p {
    font-size: 12px;
    color: #888;
    margin: 0;
}

/* 历史记录页面样式 */
.history-container {
    padding: 20px;
}

.history-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e5e9;
}

.back-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-right: 15px;
    color: #667eea;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background: #f0f0f0;
}

.history-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.history-item-date {
    font-size: 12px;
    color: #888;
    margin-bottom: 8px;
}

.history-item-preview {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.no-history {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 450px) {
    .container {
        width: 100%;
        min-height: 100vh;
        border-radius: 0;
    }
}
