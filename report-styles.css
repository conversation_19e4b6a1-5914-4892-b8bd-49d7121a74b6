/* 报告页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.report-container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 报告头部 */
.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 40px;
    height: 40px;
}

.header-text h1 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.generated-info {
    font-size: 14px;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 报告内容 */
.report-content {
    padding: 40px;
}

.project-info {
    margin-bottom: 40px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.project-info h2 {
    color: #667eea;
    font-size: 20px;
    margin-bottom: 15px;
    font-weight: 600;
}

.topic-content {
    font-size: 16px;
    color: #555;
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

/* 报告章节 */
.report-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.section {
    background: white;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    overflow: hidden;
    transition: all 0.3s ease;
}

.section:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.section h2 {
    background: #f8f9fa;
    padding: 20px 25px;
    margin: 0;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-content {
    padding: 25px;
    font-size: 15px;
    line-height: 1.7;
    color: #555;
}

.section-content h3 {
    color: #333;
    font-size: 16px;
    margin: 20px 0 10px 0;
    font-weight: 600;
}

.section-content h4 {
    color: #667eea;
    font-size: 15px;
    margin: 15px 0 8px 0;
    font-weight: 600;
}

.section-content ul, .section-content ol {
    margin: 15px 0;
    padding-left: 25px;
}

.section-content li {
    margin: 8px 0;
}

.section-content p {
    margin: 15px 0;
    text-align: justify;
}

.section-content strong {
    color: #333;
    font-weight: 600;
}

.section-content em {
    color: #667eea;
    font-style: normal;
    font-weight: 500;
}

/* 报告底部 */
.report-footer {
    background: #f8f9fa;
    padding: 25px 40px;
    text-align: center;
    border-top: 1px solid #e1e5e9;
    color: #888;
    font-size: 13px;
}

.report-footer p {
    margin: 5px 0;
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 16px;
    color: #667eea;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 打印样式 */
@media print {
    .report-header .header-actions {
        display: none;
    }
    
    .report-container {
        box-shadow: none;
    }
    
    .section {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    body {
        background: white;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .report-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .report-content {
        padding: 20px;
    }
    
    .project-info, .section-content {
        padding: 20px;
    }
    
    .report-footer {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .action-btn {
        justify-content: center;
    }
}
