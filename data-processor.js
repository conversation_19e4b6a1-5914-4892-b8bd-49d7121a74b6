// data-processor.js - 高级数据清洗和处理模块

class DataProcessor {
    constructor() {
        this.stopWords = new Set([
            // 中文停用词
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            // 英文停用词
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        ]);
        
        this.noisePatterns = [
            /cookie/gi,
            /privacy policy/gi,
            /terms of service/gi,
            /subscribe/gi,
            /newsletter/gi,
            /advertisement/gi,
            /click here/gi,
            /read more/gi,
            /learn more/gi,
            /contact us/gi,
            /follow us/gi,
            /social media/gi,
            /copyright/gi,
            /all rights reserved/gi,
            /隐私政策/gi,
            /服务条款/gi,
            /订阅/gi,
            /广告/gi,
            /点击这里/gi,
            /了解更多/gi,
            /联系我们/gi,
            /关注我们/gi,
            /版权所有/gi
        ];
    }

    /**
     * 高级数据清洗
     * @param {Object} rawData - 原始数据
     * @returns {Object} - 清洗后的数据
     */
    processData(rawData) {
        if (!rawData) return null;

        return {
            content: this.cleanContent(rawData.content),
            links: this.cleanLinks(rawData.links),
            metadata: this.cleanMetadata(rawData.metadata),
            sourceUrls: rawData.sourceUrls || [],
            levelStats: rawData.levelStats || {},
            processedAt: new Date().toISOString(),
            quality: this.assessDataQuality(rawData)
        };
    }

    /**
     * 清洗文本内容
     * @param {string} content - 原始内容
     * @returns {string} - 清洗后的内容
     */
    cleanContent(content) {
        if (!content) return '';

        let cleaned = content;

        // 1. 移除噪音模式
        this.noisePatterns.forEach(pattern => {
            cleaned = cleaned.replace(pattern, '');
        });

        // 2. 标准化空白字符
        cleaned = cleaned.replace(/\s+/g, ' ');

        // 3. 移除重复句子
        cleaned = this.removeDuplicateSentences(cleaned);

        // 4. 提取有意义的段落
        cleaned = this.extractMeaningfulParagraphs(cleaned);

        // 5. 内容摘要（如果太长）
        if (cleaned.length > 3000) {
            cleaned = this.summarizeContent(cleaned);
        }

        return cleaned.trim();
    }

    /**
     * 移除重复句子
     * @param {string} text - 文本
     * @returns {string} - 去重后的文本
     */
    removeDuplicateSentences(text) {
        const sentences = text.split(/[.。!！?？]/).map(s => s.trim()).filter(s => s.length > 10);
        const uniqueSentences = [];
        const seen = new Set();

        sentences.forEach(sentence => {
            const normalized = this.normalizeSentence(sentence);
            if (!seen.has(normalized)) {
                seen.add(normalized);
                uniqueSentences.push(sentence);
            }
        });

        return uniqueSentences.join('。') + '。';
    }

    /**
     * 标准化句子用于去重
     * @param {string} sentence - 句子
     * @returns {string} - 标准化后的句子
     */
    normalizeSentence(sentence) {
        return sentence
            .toLowerCase()
            .replace(/[^\w\s\u4e00-\u9fff]/g, '')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * 提取有意义的段落
     * @param {string} text - 文本
     * @returns {string} - 有意义的段落
     */
    extractMeaningfulParagraphs(text) {
        const paragraphs = text.split('\n').map(p => p.trim()).filter(p => p.length > 0);
        const meaningfulParagraphs = [];

        paragraphs.forEach(paragraph => {
            if (this.isMeaningfulParagraph(paragraph)) {
                meaningfulParagraphs.push(paragraph);
            }
        });

        return meaningfulParagraphs.join('\n\n');
    }

    /**
     * 判断段落是否有意义
     * @param {string} paragraph - 段落
     * @returns {boolean} - 是否有意义
     */
    isMeaningfulParagraph(paragraph) {
        // 长度检查
        if (paragraph.length < 20 || paragraph.length > 500) {
            return false;
        }

        // 检查是否包含有意义的词汇
        const meaningfulWords = this.extractMeaningfulWords(paragraph);
        if (meaningfulWords.length < 3) {
            return false;
        }

        // 检查是否为导航或菜单文本
        const navPatterns = [
            /^(home|about|contact|login|register|menu|navigation)/i,
            /^(首页|关于|联系|登录|注册|菜单|导航)/
        ];

        if (navPatterns.some(pattern => pattern.test(paragraph))) {
            return false;
        }

        return true;
    }

    /**
     * 提取有意义的词汇
     * @param {string} text - 文本
     * @returns {Array<string>} - 有意义的词汇
     */
    extractMeaningfulWords(text) {
        const words = text.split(/\s+/).map(word => word.toLowerCase().replace(/[^\w\u4e00-\u9fff]/g, ''));
        return words.filter(word => word.length > 2 && !this.stopWords.has(word));
    }

    /**
     * 内容摘要
     * @param {string} content - 内容
     * @returns {string} - 摘要
     */
    summarizeContent(content) {
        const sentences = content.split(/[.。!！?？]/).map(s => s.trim()).filter(s => s.length > 20);
        
        // 按重要性排序句子
        const scoredSentences = sentences.map(sentence => ({
            text: sentence,
            score: this.calculateSentenceScore(sentence)
        }));

        scoredSentences.sort((a, b) => b.score - a.score);

        // 取前20个最重要的句子
        const topSentences = scoredSentences.slice(0, 20).map(item => item.text);
        
        return topSentences.join('。') + '。';
    }

    /**
     * 计算句子重要性分数
     * @param {string} sentence - 句子
     * @returns {number} - 分数
     */
    calculateSentenceScore(sentence) {
        let score = 0;

        // 长度分数（适中长度得分高）
        const length = sentence.length;
        if (length >= 30 && length <= 150) {
            score += 2;
        }

        // 关键词分数
        const keywordPatterns = [
            /技术|technology|technical/gi,
            /项目|project/gi,
            /研究|research/gi,
            /开发|develop|development/gi,
            /系统|system/gi,
            /平台|platform/gi,
            /解决方案|solution/gi,
            /创新|innovation/gi,
            /分析|analysis/gi,
            /设计|design/gi
        ];

        keywordPatterns.forEach(pattern => {
            const matches = sentence.match(pattern);
            if (matches) {
                score += matches.length;
            }
        });

        // 数字和数据分数
        if (/\d+/.test(sentence)) {
            score += 1;
        }

        return score;
    }

    /**
     * 清洗链接
     * @param {Array<string>} links - 链接数组
     * @returns {Array<string>} - 清洗后的链接
     */
    cleanLinks(links) {
        if (!links || !Array.isArray(links)) return [];

        return links
            .filter(link => this.isValidCleanLink(link))
            .map(link => this.normalizeLink(link))
            .filter((link, index, arr) => arr.indexOf(link) === index) // 去重
            .slice(0, 30); // 限制数量
    }

    /**
     * 检查链接是否有效且干净
     * @param {string} link - 链接
     * @returns {boolean} - 是否有效
     */
    isValidCleanLink(link) {
        if (!link || typeof link !== 'string') return false;

        // 排除无效链接
        const invalidPatterns = [
            /javascript:/i,
            /mailto:/i,
            /tel:/i,
            /#$/,
            /\.(jpg|jpeg|png|gif|pdf|doc|docx|zip|rar)$/i
        ];

        return !invalidPatterns.some(pattern => pattern.test(link));
    }

    /**
     * 标准化链接
     * @param {string} link - 链接
     * @returns {string} - 标准化后的链接
     */
    normalizeLink(link) {
        try {
            const url = new URL(link);
            // 移除查询参数中的跟踪参数
            const trackingParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'ref', 'source'];
            trackingParams.forEach(param => {
                url.searchParams.delete(param);
            });
            return url.toString();
        } catch (error) {
            return link;
        }
    }

    /**
     * 清洗元数据
     * @param {Object} metadata - 元数据
     * @returns {Object} - 清洗后的元数据
     */
    cleanMetadata(metadata) {
        if (!metadata || typeof metadata !== 'object') return {};

        const cleaned = {};

        // 清洗标题
        if (metadata.title) {
            cleaned.title = this.cleanText(metadata.title, 100);
        }

        // 清洗描述
        if (metadata.description) {
            cleaned.description = this.cleanText(metadata.description, 300);
        }

        // 清洗关键词
        if (metadata.keywords) {
            cleaned.keywords = this.cleanKeywords(metadata.keywords);
        }

        // 保留URL和域名
        if (metadata.url) {
            cleaned.url = metadata.url;
        }

        if (metadata.domain) {
            cleaned.domain = metadata.domain;
        }

        return cleaned;
    }

    /**
     * 清洗文本
     * @param {string} text - 文本
     * @param {number} maxLength - 最大长度
     * @returns {string} - 清洗后的文本
     */
    cleanText(text, maxLength = 200) {
        if (!text) return '';

        let cleaned = text
            .replace(/\s+/g, ' ')
            .trim();

        if (cleaned.length > maxLength) {
            cleaned = cleaned.substring(0, maxLength) + '...';
        }

        return cleaned;
    }

    /**
     * 清洗关键词
     * @param {string} keywords - 关键词字符串
     * @returns {string} - 清洗后的关键词
     */
    cleanKeywords(keywords) {
        if (!keywords) return '';

        return keywords
            .split(',')
            .map(keyword => keyword.trim())
            .filter(keyword => keyword.length > 1 && keyword.length < 30)
            .slice(0, 10)
            .join(', ');
    }

    /**
     * 评估数据质量
     * @param {Object} data - 数据
     * @returns {Object} - 质量评估
     */
    assessDataQuality(data) {
        const quality = {
            score: 0,
            factors: {},
            level: 'low'
        };

        // 内容质量
        if (data.content) {
            const contentLength = data.content.length;
            if (contentLength > 500) quality.score += 30;
            else if (contentLength > 200) quality.score += 20;
            else if (contentLength > 50) quality.score += 10;
            
            quality.factors.contentLength = contentLength;
        }

        // 链接数量
        if (data.links && data.links.length > 0) {
            const linkCount = data.links.length;
            if (linkCount > 10) quality.score += 20;
            else if (linkCount > 5) quality.score += 15;
            else if (linkCount > 0) quality.score += 10;
            
            quality.factors.linkCount = linkCount;
        }

        // 元数据完整性
        if (data.metadata) {
            if (data.metadata.title) quality.score += 10;
            if (data.metadata.description) quality.score += 10;
            if (data.metadata.keywords) quality.score += 5;
            
            quality.factors.hasMetadata = true;
        }

        // 多层级数据
        if (data.levelStats) {
            const totalPages = data.levelStats.totalPages || 0;
            if (totalPages > 10) quality.score += 15;
            else if (totalPages > 5) quality.score += 10;
            else if (totalPages > 1) quality.score += 5;
            
            quality.factors.totalPages = totalPages;
        }

        // 确定质量等级
        if (quality.score >= 70) quality.level = 'high';
        else if (quality.score >= 40) quality.level = 'medium';
        else quality.level = 'low';

        return quality;
    }
}

// 创建全局实例
const dataProcessor = new DataProcessor();

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.dataProcessor = dataProcessor;
}
